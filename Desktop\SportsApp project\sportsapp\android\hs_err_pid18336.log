#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 180355072 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3898), pid=18336, tid=19632
#
# JRE version: OpenJDK Runtime Environment (21.0.6) (build 21.0.6+-13368085-b895.109)
# Java VM: OpenJDK 64-Bit Server VM (21.0.6+-13368085-b895.109, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError -Xmx8G -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12

Host: AMD Ryzen 7 PRO 4750U with Radeon Graphics, 16 cores, 15G,  Windows 11 , 64 bit Build 22000 (10.0.22000.2538)
Time: Wed Jun  4 06:38:16 2025 Egypt Daylight Time elapsed time: 45.740818 seconds (0d 0h 0m 45s)

---------------  T H R E A D  ---------------

Current thread (0x000001493b154870):  VMThread "VM Thread"          [id=19632, stack(0x00000002bf000000,0x00000002bf100000) (1024K)]

Stack: [0x00000002bf000000,0x00000002bf100000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d0639]
V  [jvm.dll+0x85eb03]
V  [jvm.dll+0x86105e]
V  [jvm.dll+0x861743]
V  [jvm.dll+0x27e6e6]
V  [jvm.dll+0x6ccfd5]
V  [jvm.dll+0x6c0a8a]
V  [jvm.dll+0x35537b]
V  [jvm.dll+0x35cfd6]
V  [jvm.dll+0x3aef86]
V  [jvm.dll+0x3af258]
V  [jvm.dll+0x327a2c]
V  [jvm.dll+0x327ab7]
V  [jvm.dll+0x36d93c]
V  [jvm.dll+0x36c0ee]
V  [jvm.dll+0x327181]
V  [jvm.dll+0x36b7f0]
V  [jvm.dll+0x8674c8]
V  [jvm.dll+0x868844]
V  [jvm.dll+0x868d80]
V  [jvm.dll+0x869013]
V  [jvm.dll+0x806ed8]
V  [jvm.dll+0x6cef0d]
C  [ucrtbase.dll+0x26c0c]
C  [KERNEL32.DLL+0x153e0]
C  [ntdll.dll+0x485b]

VM_Operation (0x00000002c71fa2f0): G1CollectForAllocation, mode: safepoint, requested by thread 0x000001494729cf50


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001495023cc00, length=233, elements={
0x0000014908a56e50, 0x000001494007b8d0, 0x00000149400ab980, 0x00000149400aebe0,
0x00000149400ac690, 0x00000149400ad4e0, 0x00000149400d1920, 0x00000149400dc6c0,
0x00000149400de160, 0x0000014940247c30, 0x00000149404073d0, 0x0000014941199260,
0x0000014941ef2bc0, 0x00000149422222e0, 0x0000014941f54800, 0x00000149421f1250,
0x00000149420de480, 0x000001494218e730, 0x000001494218edc0, 0x000001494218f450,
0x000001494218fae0, 0x0000014942190170, 0x0000014942190800, 0x0000014942190e90,
0x000001494218e0a0, 0x00000149458779b0, 0x0000014945872460, 0x0000014945877320,
0x0000014945872af0, 0x0000014945878d60, 0x0000014945876c90, 0x0000014945874bc0,
0x00000149458793f0, 0x0000014945876600, 0x0000014944563f50, 0x0000014944561160,
0x00000149445673d0, 0x0000014944564c70, 0x0000014944561e80, 0x0000014944562510,
0x0000014944566d40, 0x00000149445638c0, 0x0000014944565300, 0x0000014944562ba0,
0x00000149445666b0, 0x0000014944563230, 0x0000014944566020, 0x0000014944560ad0,
0x00000149445617f0, 0x00000149445645e0, 0x0000014944565990, 0x0000014944567a60,
0x00000149445680f0, 0x000001494476c2e0, 0x00000149447694f0, 0x0000014944769b80,
0x000001494476c970, 0x000001494476fdf0, 0x000001494476e3b0, 0x000001494476af30,
0x000001494476f760, 0x000001494476dd20, 0x000001494476d000, 0x000001494476d690,
0x000001494476ea40, 0x000001494476a210, 0x000001494476f0d0, 0x000001494476bc50,
0x000001494476a8a0, 0x000001494476b5c0, 0x0000014944768e60, 0x0000014944770480,
0x0000014945229d20, 0x000001494522d1a0, 0x0000014945229000, 0x000001494522e550,
0x000001494522a3b0, 0x000001494522ebe0, 0x000001494522aa40, 0x000001494522b0d0,
0x0000014945d9f810, 0x0000014945d9eaf0, 0x0000014945d9f180, 0x0000014945da4040,
0x0000014945d9e460, 0x0000014945d9fea0, 0x0000014945da0530, 0x0000014945da0bc0,
0x0000014945da4d60, 0x0000014945da2600, 0x0000014945da53f0, 0x0000014945da2c90,
0x0000014945da1250, 0x0000014945da39b0, 0x0000014945da5a80, 0x0000014945da18e0,
0x0000014945da46d0, 0x0000014945da1f70, 0x0000014945da3320, 0x000001494729dc70,
0x000001494729d5e0, 0x00000149472a0a60, 0x00000149472a03d0, 0x0000014947299ad0,
0x000001494729a7f0, 0x000001494729a160, 0x000001494729e300, 0x000001494729e990,
0x000001494729f020, 0x000001494729fd40, 0x000001494729cf50, 0x000001494729f6b0,
0x00000149472a10f0, 0x000001494729ae80, 0x000001494729b510, 0x000001494729bba0,
0x000001494729c230, 0x000001494729c8c0, 0x0000014947ad8ed0, 0x0000014947ad6e00,
0x0000014947ad4010, 0x0000014947ad9560, 0x0000014947ad7490, 0x0000014947ad7b20,
0x0000014947ad81b0, 0x0000014947ad9bf0, 0x0000014947ada280, 0x0000014947ada910,
0x0000014947ad8840, 0x0000014947ad5a50, 0x0000014947ad3980, 0x0000014947adafa0,
0x0000014947ad46a0, 0x0000014947ad4d30, 0x0000014947ad53c0, 0x0000014947ad6770,
0x0000014947ad60e0, 0x00000149470dad80, 0x00000149470de200, 0x00000149470e0ff0,
0x00000149470e1680, 0x00000149470de890, 0x00000149470e1d10, 0x00000149470def20,
0x00000149470dd4e0, 0x00000149470df5b0, 0x00000149470dfc40, 0x00000149470e23a0,
0x00000149470e02d0, 0x00000149470db410, 0x00000149470dbaa0, 0x00000149470dce50,
0x00000149470dc7c0, 0x00000149470e0960, 0x00000149470ddb70, 0x0000014941590010,
0x0000014941595bf0, 0x0000014941592e00, 0x0000014941596910, 0x0000014941591a50,
0x000001494158f980, 0x0000014941590d30, 0x0000014941595560, 0x0000014941593490,
0x0000014941593b20, 0x0000014941597630, 0x0000014941592770, 0x00000149415941b0,
0x00000149415906a0, 0x0000014941596280, 0x0000014941597cc0, 0x0000014941594840,
0x0000014941594ed0, 0x0000014941596fa0, 0x0000014941598350, 0x00000149415913c0,
0x00000149415989e0, 0x0000014941599070, 0x00000149415920e0, 0x0000014941599700,
0x000001494159b140, 0x000001494159c4f0, 0x000001494159cb80, 0x000001494159be60,
0x000001494159e5c0, 0x000001494159df30, 0x000001494159ec50, 0x000001494159d210,
0x000001494159d8a0, 0x00000149440e6240, 0x00000149440ebe20, 0x00000149440ecb40,
0x00000149440ed860, 0x00000149440eec10, 0x00000149440edef0, 0x00000149440ef2a0,
0x0000014945873810, 0x0000014943d3dba0, 0x0000014943d3e8c0, 0x0000014943d39a00,
0x0000014943d3fc70, 0x0000014943d3e230, 0x0000014943d3a720, 0x0000014943d3b440,
0x0000014943d3ce80, 0x0000014943d3ef50, 0x0000014943d3f5e0, 0x0000014943d39370,
0x0000014943d40990, 0x0000014943d41020, 0x0000014943d3c7f0, 0x0000014943d40300,
0x0000014943d3bad0, 0x0000014943d3a090, 0x0000014943d416b0, 0x0000014943d3d510,
0x0000014943d444a0, 0x0000014943d44b30, 0x0000014943d46c00, 0x00000149440ea3e0,
0x00000149456fecc0, 0x00000149456fab20, 0x00000149456fc560, 0x00000149456ff350,
0x00000149456fb1b0, 0x00000149456f83c0, 0x00000149456fd280, 0x00000149456fb840,
0x00000149412d9f70, 0x00000149412da640, 0x00000149412d91d0, 0x00000149412dbab0,
0x00000149502de790
}

Java Threads: ( => current thread )
  0x0000014908a56e50 JavaThread "main"                              [_thread_blocked, id=18344, stack(0x00000002be900000,0x00000002bea00000) (1024K)]
  0x000001494007b8d0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=10792, stack(0x00000002bf100000,0x00000002bf200000) (1024K)]
  0x00000149400ab980 JavaThread "Finalizer"                  daemon [_thread_blocked, id=20072, stack(0x00000002bf200000,0x00000002bf300000) (1024K)]
  0x00000149400aebe0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=1504, stack(0x00000002bf300000,0x00000002bf400000) (1024K)]
  0x00000149400ac690 JavaThread "Attach Listener"            daemon [_thread_blocked, id=15340, stack(0x00000002bf400000,0x00000002bf500000) (1024K)]
  0x00000149400ad4e0 JavaThread "Service Thread"             daemon [_thread_blocked, id=14048, stack(0x00000002bf500000,0x00000002bf600000) (1024K)]
  0x00000149400d1920 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=17512, stack(0x00000002bf600000,0x00000002bf700000) (1024K)]
  0x00000149400dc6c0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=5316, stack(0x00000002bf700000,0x00000002bf800000) (1024K)]
  0x00000149400de160 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=8368, stack(0x00000002bf800000,0x00000002bf900000) (1024K)]
  0x0000014940247c30 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=20060, stack(0x00000002bf900000,0x00000002bfa00000) (1024K)]
  0x00000149404073d0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=13192, stack(0x00000002bfd00000,0x00000002bfe00000) (1024K)]
  0x0000014941199260 JavaThread "Daemon health stats"               [_thread_blocked, id=19904, stack(0x00000002c0400000,0x00000002c0500000) (1024K)]
  0x0000014941ef2bc0 JavaThread "Incoming local TCP Connector on port 6489"        [_thread_in_native, id=17352, stack(0x00000002c0500000,0x00000002c0600000) (1024K)]
  0x00000149422222e0 JavaThread "Daemon periodic checks"            [_thread_blocked, id=20076, stack(0x00000002c0d00000,0x00000002c0e00000) (1024K)]
  0x0000014941f54800 JavaThread "Daemon"                            [_thread_blocked, id=16780, stack(0x00000002c0e00000,0x00000002c0f00000) (1024K)]
  0x00000149421f1250 JavaThread "Handler for socket connection from /127.0.0.1:6489 to /127.0.0.1:6490"        [_thread_in_native, id=17920, stack(0x00000002c0f00000,0x00000002c1000000) (1024K)]
  0x00000149420de480 JavaThread "Cancel handler"                    [_thread_blocked, id=7548, stack(0x00000002c1000000,0x00000002c1100000) (1024K)]
  0x000001494218e730 JavaThread "Daemon worker"                     [_thread_blocked, id=20532, stack(0x00000002c1100000,0x00000002c1200000) (1024K)]
  0x000001494218edc0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:6489 to /127.0.0.1:6490"        [_thread_blocked, id=18944, stack(0x00000002c1200000,0x00000002c1300000) (1024K)]
  0x000001494218f450 JavaThread "Stdin handler"                     [_thread_blocked, id=2772, stack(0x00000002c1300000,0x00000002c1400000) (1024K)]
  0x000001494218fae0 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=19108, stack(0x00000002c1400000,0x00000002c1500000) (1024K)]
  0x0000014942190170 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=20204, stack(0x00000002c1800000,0x00000002c1900000) (1024K)]
  0x0000014942190800 JavaThread "File lock request listener"        [_thread_in_native, id=19280, stack(0x00000002c1900000,0x00000002c1a00000) (1024K)]
  0x0000014942190e90 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.12\fileHashes)"        [_thread_blocked, id=17752, stack(0x00000002c1a00000,0x00000002c1b00000) (1024K)]
  0x000001494218e0a0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\Desktop\SportsApp project\sportsapp\android\.gradle\8.12\fileHashes)"        [_thread_blocked, id=16060, stack(0x00000002c1c00000,0x00000002c1d00000) (1024K)]
  0x00000149458779b0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\Desktop\SportsApp project\sportsapp\android\.gradle\buildOutputCleanup)"        [_thread_blocked, id=3020, stack(0x00000002c0300000,0x00000002c0400000) (1024K)]
  0x0000014945872460 JavaThread "File watcher server"        daemon [_thread_blocked, id=3236, stack(0x00000002c1d00000,0x00000002c1e00000) (1024K)]
  0x0000014945877320 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=13436, stack(0x00000002c1e00000,0x00000002c1f00000) (1024K)]
  0x0000014945872af0 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\Desktop\SportsApp project\sportsapp\android\.gradle\8.12\checksums)"        [_thread_blocked, id=14432, stack(0x00000002c2100000,0x00000002c2200000) (1024K)]
  0x0000014945878d60 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.12\fileContent)"        [_thread_blocked, id=18084, stack(0x00000002c2200000,0x00000002c2300000) (1024K)]
  0x0000014945876c90 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.12\md-rule)"        [_thread_blocked, id=20368, stack(0x00000002c2300000,0x00000002c2400000) (1024K)]
  0x0000014945874bc0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.12\md-supplier)"        [_thread_blocked, id=2628, stack(0x00000002c2400000,0x00000002c2500000) (1024K)]
  0x00000149458793f0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\flutter\packages\flutter_tools\gradle\.gradle\buildOutputCleanup)"        [_thread_blocked, id=15184, stack(0x00000002c2000000,0x00000002c2100000) (1024K)]
  0x0000014945876600 JavaThread "Unconstrained build operations"        [_thread_blocked, id=2952, stack(0x00000002c1f00000,0x00000002c2000000) (1024K)]
  0x0000014944563f50 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=2464, stack(0x00000002c2500000,0x00000002c2600000) (1024K)]
  0x0000014944561160 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=6048, stack(0x00000002c2600000,0x00000002c2700000) (1024K)]
  0x00000149445673d0 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=19572, stack(0x00000002c2700000,0x00000002c2800000) (1024K)]
  0x0000014944564c70 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=15732, stack(0x00000002c2800000,0x00000002c2900000) (1024K)]
  0x0000014944561e80 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=21324, stack(0x00000002c2900000,0x00000002c2a00000) (1024K)]
  0x0000014944562510 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=16924, stack(0x00000002c2a00000,0x00000002c2b00000) (1024K)]
  0x0000014944566d40 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=1364, stack(0x00000002c2b00000,0x00000002c2c00000) (1024K)]
  0x00000149445638c0 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=17112, stack(0x00000002c2c00000,0x00000002c2d00000) (1024K)]
  0x0000014944565300 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=15912, stack(0x00000002c2d00000,0x00000002c2e00000) (1024K)]
  0x0000014944562ba0 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=2912, stack(0x00000002c2e00000,0x00000002c2f00000) (1024K)]
  0x00000149445666b0 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=15684, stack(0x00000002c2f00000,0x00000002c3000000) (1024K)]
  0x0000014944563230 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=2820, stack(0x00000002c3000000,0x00000002c3100000) (1024K)]
  0x0000014944566020 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=15244, stack(0x00000002c3100000,0x00000002c3200000) (1024K)]
  0x0000014944560ad0 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=12032, stack(0x00000002c3200000,0x00000002c3300000) (1024K)]
  0x00000149445617f0 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=14600, stack(0x00000002c3300000,0x00000002c3400000) (1024K)]
  0x00000149445645e0 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=9720, stack(0x00000002c3400000,0x00000002c3500000) (1024K)]
  0x0000014944565990 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=21076, stack(0x00000002c3500000,0x00000002c3600000) (1024K)]
  0x0000014944567a60 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=13468, stack(0x00000002c3600000,0x00000002c3700000) (1024K)]
  0x00000149445680f0 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=372, stack(0x00000002c3700000,0x00000002c3800000) (1024K)]
  0x000001494476c2e0 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=20248, stack(0x00000002c3800000,0x00000002c3900000) (1024K)]
  0x00000149447694f0 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=11800, stack(0x00000002c3900000,0x00000002c3a00000) (1024K)]
  0x0000014944769b80 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=3636, stack(0x00000002c3a00000,0x00000002c3b00000) (1024K)]
  0x000001494476c970 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=20848, stack(0x00000002c3b00000,0x00000002c3c00000) (1024K)]
  0x000001494476fdf0 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=19356, stack(0x00000002c3c00000,0x00000002c3d00000) (1024K)]
  0x000001494476e3b0 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=3716, stack(0x00000002c3d00000,0x00000002c3e00000) (1024K)]
  0x000001494476af30 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=20212, stack(0x00000002c3e00000,0x00000002c3f00000) (1024K)]
  0x000001494476f760 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=10524, stack(0x00000002c3f00000,0x00000002c4000000) (1024K)]
  0x000001494476dd20 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=21160, stack(0x00000002c4000000,0x00000002c4100000) (1024K)]
  0x000001494476d000 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=10516, stack(0x00000002c4100000,0x00000002c4200000) (1024K)]
  0x000001494476d690 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=15648, stack(0x00000002c4200000,0x00000002c4300000) (1024K)]
  0x000001494476ea40 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=3444, stack(0x00000002c4300000,0x00000002c4400000) (1024K)]
  0x000001494476a210 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=15468, stack(0x00000002c4400000,0x00000002c4500000) (1024K)]
  0x000001494476f0d0 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=21052, stack(0x00000002c4500000,0x00000002c4600000) (1024K)]
  0x000001494476bc50 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=3992, stack(0x00000002c4600000,0x00000002c4700000) (1024K)]
  0x000001494476a8a0 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=10520, stack(0x00000002c4700000,0x00000002c4800000) (1024K)]
  0x000001494476b5c0 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=16004, stack(0x00000002c4800000,0x00000002c4900000) (1024K)]
  0x0000014944768e60 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=8560, stack(0x00000002c4900000,0x00000002c4a00000) (1024K)]
  0x0000014944770480 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=10744, stack(0x00000002c4a00000,0x00000002c4b00000) (1024K)]
  0x0000014945229d20 JavaThread "Unconstrained build operations Thread 40"        [_thread_blocked, id=12028, stack(0x00000002c4b00000,0x00000002c4c00000) (1024K)]
  0x000001494522d1a0 JavaThread "Unconstrained build operations Thread 41"        [_thread_blocked, id=14240, stack(0x00000002c4c00000,0x00000002c4d00000) (1024K)]
  0x0000014945229000 JavaThread "Unconstrained build operations Thread 42"        [_thread_blocked, id=18912, stack(0x00000002c4d00000,0x00000002c4e00000) (1024K)]
  0x000001494522e550 JavaThread "Unconstrained build operations Thread 43"        [_thread_blocked, id=12928, stack(0x00000002c4e00000,0x00000002c4f00000) (1024K)]
  0x000001494522a3b0 JavaThread "Unconstrained build operations Thread 44"        [_thread_blocked, id=21496, stack(0x00000002c4f00000,0x00000002c5000000) (1024K)]
  0x000001494522ebe0 JavaThread "Unconstrained build operations Thread 45"        [_thread_blocked, id=19956, stack(0x00000002c5000000,0x00000002c5100000) (1024K)]
  0x000001494522aa40 JavaThread "Memory manager"                    [_thread_blocked, id=16832, stack(0x00000002bfe00000,0x00000002bff00000) (1024K)]
  0x000001494522b0d0 JavaThread "build event listener"              [_thread_blocked, id=19896, stack(0x00000002c5200000,0x00000002c5300000) (1024K)]
  0x0000014945d9f810 JavaThread "included builds"                   [_thread_blocked, id=11000, stack(0x00000002c5300000,0x00000002c5400000) (1024K)]
  0x0000014945d9eaf0 JavaThread "Execution worker"                  [_thread_blocked, id=18672, stack(0x00000002c5400000,0x00000002c5500000) (1024K)]
  0x0000014945d9f180 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=21184, stack(0x00000002c5500000,0x00000002c5600000) (1024K)]
  0x0000014945da4040 JavaThread "Execution worker Thread 3"         [_thread_blocked, id=19408, stack(0x00000002c5600000,0x00000002c5700000) (1024K)]
  0x0000014945d9e460 JavaThread "Execution worker Thread 4"         [_thread_blocked, id=9412, stack(0x00000002c5700000,0x00000002c5800000) (1024K)]
  0x0000014945d9fea0 JavaThread "Execution worker Thread 5"         [_thread_blocked, id=16560, stack(0x00000002c5800000,0x00000002c5900000) (1024K)]
  0x0000014945da0530 JavaThread "Execution worker Thread 6"         [_thread_blocked, id=17424, stack(0x00000002c5900000,0x00000002c5a00000) (1024K)]
  0x0000014945da0bc0 JavaThread "Execution worker Thread 7"         [_thread_blocked, id=20444, stack(0x00000002c5a00000,0x00000002c5b00000) (1024K)]
  0x0000014945da4d60 JavaThread "Execution worker Thread 8"         [_thread_blocked, id=17156, stack(0x00000002c5b00000,0x00000002c5c00000) (1024K)]
  0x0000014945da2600 JavaThread "Execution worker Thread 9"         [_thread_blocked, id=17448, stack(0x00000002c5c00000,0x00000002c5d00000) (1024K)]
  0x0000014945da53f0 JavaThread "Execution worker Thread 10"        [_thread_blocked, id=1352, stack(0x00000002c5d00000,0x00000002c5e00000) (1024K)]
  0x0000014945da2c90 JavaThread "Execution worker Thread 11"        [_thread_blocked, id=17592, stack(0x00000002c5e00000,0x00000002c5f00000) (1024K)]
  0x0000014945da1250 JavaThread "Execution worker Thread 12"        [_thread_blocked, id=16160, stack(0x00000002c5f00000,0x00000002c6000000) (1024K)]
  0x0000014945da39b0 JavaThread "Execution worker Thread 13"        [_thread_blocked, id=20952, stack(0x00000002c6000000,0x00000002c6100000) (1024K)]
  0x0000014945da5a80 JavaThread "Execution worker Thread 14"        [_thread_blocked, id=15376, stack(0x00000002c6100000,0x00000002c6200000) (1024K)]
  0x0000014945da18e0 JavaThread "Execution worker Thread 15"        [_thread_blocked, id=18512, stack(0x00000002c6200000,0x00000002c6300000) (1024K)]
  0x0000014945da46d0 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\flutter\packages\flutter_tools\gradle\.gradle\8.12\executionHistory)"        [_thread_blocked, id=8040, stack(0x00000002c6300000,0x00000002c6400000) (1024K)]
  0x0000014945da1f70 JavaThread "Unconstrained build operations Thread 46"        [_thread_blocked, id=3436, stack(0x00000002c6400000,0x00000002c6500000) (1024K)]
  0x0000014945da3320 JavaThread "Unconstrained build operations Thread 47"        [_thread_blocked, id=7092, stack(0x00000002c6500000,0x00000002c6600000) (1024K)]
  0x000001494729dc70 JavaThread "Unconstrained build operations Thread 48"        [_thread_blocked, id=3988, stack(0x00000002c6600000,0x00000002c6700000) (1024K)]
  0x000001494729d5e0 JavaThread "Unconstrained build operations Thread 49"        [_thread_blocked, id=928, stack(0x00000002c6700000,0x00000002c6800000) (1024K)]
  0x00000149472a0a60 JavaThread "Unconstrained build operations Thread 50"        [_thread_blocked, id=21212, stack(0x00000002c6800000,0x00000002c6900000) (1024K)]
  0x00000149472a03d0 JavaThread "Unconstrained build operations Thread 51"        [_thread_blocked, id=15288, stack(0x00000002c6900000,0x00000002c6a00000) (1024K)]
  0x0000014947299ad0 JavaThread "Unconstrained build operations Thread 52"        [_thread_blocked, id=13116, stack(0x00000002c6a00000,0x00000002c6b00000) (1024K)]
  0x000001494729a7f0 JavaThread "Unconstrained build operations Thread 53"        [_thread_blocked, id=11276, stack(0x00000002c6b00000,0x00000002c6c00000) (1024K)]
  0x000001494729a160 JavaThread "Unconstrained build operations Thread 54"        [_thread_blocked, id=14480, stack(0x00000002c6c00000,0x00000002c6d00000) (1024K)]
  0x000001494729e300 JavaThread "Unconstrained build operations Thread 55"        [_thread_blocked, id=19076, stack(0x00000002c6d00000,0x00000002c6e00000) (1024K)]
  0x000001494729e990 JavaThread "Unconstrained build operations Thread 56"        [_thread_blocked, id=4200, stack(0x00000002c6e00000,0x00000002c6f00000) (1024K)]
  0x000001494729f020 JavaThread "Unconstrained build operations Thread 57"        [_thread_blocked, id=11020, stack(0x00000002c6f00000,0x00000002c7000000) (1024K)]
  0x000001494729fd40 JavaThread "Unconstrained build operations Thread 58"        [_thread_blocked, id=6988, stack(0x00000002c7000000,0x00000002c7100000) (1024K)]
  0x000001494729cf50 JavaThread "Unconstrained build operations Thread 59"        [_thread_blocked, id=15100, stack(0x00000002c7100000,0x00000002c7200000) (1024K)]
  0x000001494729f6b0 JavaThread "Unconstrained build operations Thread 60"        [_thread_blocked, id=15156, stack(0x00000002c7200000,0x00000002c7300000) (1024K)]
  0x00000149472a10f0 JavaThread "Unconstrained build operations Thread 61"        [_thread_blocked, id=6528, stack(0x00000002c7300000,0x00000002c7400000) (1024K)]
  0x000001494729ae80 JavaThread "Unconstrained build operations Thread 62"        [_thread_blocked, id=16776, stack(0x00000002c7400000,0x00000002c7500000) (1024K)]
  0x000001494729b510 JavaThread "Unconstrained build operations Thread 63"        [_thread_blocked, id=18212, stack(0x00000002c7500000,0x00000002c7600000) (1024K)]
  0x000001494729bba0 JavaThread "Unconstrained build operations Thread 64"        [_thread_blocked, id=2224, stack(0x00000002c7600000,0x00000002c7700000) (1024K)]
  0x000001494729c230 JavaThread "Unconstrained build operations Thread 65"        [_thread_blocked, id=16236, stack(0x00000002c7700000,0x00000002c7800000) (1024K)]
  0x000001494729c8c0 JavaThread "Unconstrained build operations Thread 66"        [_thread_blocked, id=2508, stack(0x00000002c7800000,0x00000002c7900000) (1024K)]
  0x0000014947ad8ed0 JavaThread "Unconstrained build operations Thread 67"        [_thread_blocked, id=16988, stack(0x00000002c7900000,0x00000002c7a00000) (1024K)]
  0x0000014947ad6e00 JavaThread "Unconstrained build operations Thread 68"        [_thread_blocked, id=8704, stack(0x00000002c7a00000,0x00000002c7b00000) (1024K)]
  0x0000014947ad4010 JavaThread "Unconstrained build operations Thread 69"        [_thread_blocked, id=7052, stack(0x00000002c7b00000,0x00000002c7c00000) (1024K)]
  0x0000014947ad9560 JavaThread "Unconstrained build operations Thread 70"        [_thread_blocked, id=10528, stack(0x00000002c7c00000,0x00000002c7d00000) (1024K)]
  0x0000014947ad7490 JavaThread "Unconstrained build operations Thread 71"        [_thread_blocked, id=11948, stack(0x00000002c7d00000,0x00000002c7e00000) (1024K)]
  0x0000014947ad7b20 JavaThread "Unconstrained build operations Thread 72"        [_thread_blocked, id=4160, stack(0x00000002c7e00000,0x00000002c7f00000) (1024K)]
  0x0000014947ad81b0 JavaThread "Unconstrained build operations Thread 73"        [_thread_blocked, id=14968, stack(0x00000002c7f00000,0x00000002c8000000) (1024K)]
  0x0000014947ad9bf0 JavaThread "Unconstrained build operations Thread 74"        [_thread_blocked, id=18664, stack(0x00000002c8000000,0x00000002c8100000) (1024K)]
  0x0000014947ada280 JavaThread "Unconstrained build operations Thread 75"        [_thread_blocked, id=3468, stack(0x00000002c8100000,0x00000002c8200000) (1024K)]
  0x0000014947ada910 JavaThread "Unconstrained build operations Thread 76"        [_thread_blocked, id=6304, stack(0x00000002c8200000,0x00000002c8300000) (1024K)]
  0x0000014947ad8840 JavaThread "Unconstrained build operations Thread 77"        [_thread_blocked, id=21380, stack(0x00000002c8300000,0x00000002c8400000) (1024K)]
  0x0000014947ad5a50 JavaThread "Unconstrained build operations Thread 78"        [_thread_blocked, id=15096, stack(0x00000002c8400000,0x00000002c8500000) (1024K)]
  0x0000014947ad3980 JavaThread "Unconstrained build operations Thread 79"        [_thread_blocked, id=19328, stack(0x00000002c8500000,0x00000002c8600000) (1024K)]
  0x0000014947adafa0 JavaThread "Unconstrained build operations Thread 80"        [_thread_blocked, id=10700, stack(0x00000002c8600000,0x00000002c8700000) (1024K)]
  0x0000014947ad46a0 JavaThread "Unconstrained build operations Thread 81"        [_thread_blocked, id=16304, stack(0x00000002c8700000,0x00000002c8800000) (1024K)]
  0x0000014947ad4d30 JavaThread "Unconstrained build operations Thread 82"        [_thread_blocked, id=21296, stack(0x00000002c8800000,0x00000002c8900000) (1024K)]
  0x0000014947ad53c0 JavaThread "Unconstrained build operations Thread 83"        [_thread_blocked, id=19952, stack(0x00000002c8900000,0x00000002c8a00000) (1024K)]
  0x0000014947ad6770 JavaThread "Unconstrained build operations Thread 84"        [_thread_blocked, id=20932, stack(0x00000002c8a00000,0x00000002c8b00000) (1024K)]
  0x0000014947ad60e0 JavaThread "Unconstrained build operations Thread 85"        [_thread_blocked, id=4512, stack(0x00000002c8b00000,0x00000002c8c00000) (1024K)]
  0x00000149470dad80 JavaThread "Unconstrained build operations Thread 86"        [_thread_blocked, id=5828, stack(0x00000002c8c00000,0x00000002c8d00000) (1024K)]
  0x00000149470de200 JavaThread "Unconstrained build operations Thread 87"        [_thread_blocked, id=19528, stack(0x00000002c8d00000,0x00000002c8e00000) (1024K)]
  0x00000149470e0ff0 JavaThread "Unconstrained build operations Thread 88"        [_thread_blocked, id=14964, stack(0x00000002c8e00000,0x00000002c8f00000) (1024K)]
  0x00000149470e1680 JavaThread "Unconstrained build operations Thread 89"        [_thread_blocked, id=15792, stack(0x00000002c8f00000,0x00000002c9000000) (1024K)]
  0x00000149470de890 JavaThread "Unconstrained build operations Thread 90"        [_thread_blocked, id=4284, stack(0x00000002c9000000,0x00000002c9100000) (1024K)]
  0x00000149470e1d10 JavaThread "Unconstrained build operations Thread 91"        [_thread_blocked, id=6844, stack(0x00000002c9100000,0x00000002c9200000) (1024K)]
  0x00000149470def20 JavaThread "Unconstrained build operations Thread 92"        [_thread_blocked, id=2432, stack(0x00000002c9200000,0x00000002c9300000) (1024K)]
  0x00000149470dd4e0 JavaThread "Unconstrained build operations Thread 93"        [_thread_blocked, id=18744, stack(0x00000002c9300000,0x00000002c9400000) (1024K)]
  0x00000149470df5b0 JavaThread "Unconstrained build operations Thread 94"        [_thread_blocked, id=7556, stack(0x00000002c9400000,0x00000002c9500000) (1024K)]
  0x00000149470dfc40 JavaThread "Unconstrained build operations Thread 95"        [_thread_blocked, id=17876, stack(0x00000002c9500000,0x00000002c9600000) (1024K)]
  0x00000149470e23a0 JavaThread "Unconstrained build operations Thread 96"        [_thread_blocked, id=8416, stack(0x00000002c9600000,0x00000002c9700000) (1024K)]
  0x00000149470e02d0 JavaThread "Unconstrained build operations Thread 97"        [_thread_blocked, id=19568, stack(0x00000002c9700000,0x00000002c9800000) (1024K)]
  0x00000149470db410 JavaThread "Unconstrained build operations Thread 98"        [_thread_blocked, id=6368, stack(0x00000002c9800000,0x00000002c9900000) (1024K)]
  0x00000149470dbaa0 JavaThread "Unconstrained build operations Thread 99"        [_thread_blocked, id=9500, stack(0x00000002c9900000,0x00000002c9a00000) (1024K)]
  0x00000149470dce50 JavaThread "Unconstrained build operations Thread 100"        [_thread_blocked, id=20200, stack(0x00000002c9a00000,0x00000002c9b00000) (1024K)]
  0x00000149470dc7c0 JavaThread "Unconstrained build operations Thread 101"        [_thread_blocked, id=15148, stack(0x00000002c9b00000,0x00000002c9c00000) (1024K)]
  0x00000149470e0960 JavaThread "Unconstrained build operations Thread 102"        [_thread_blocked, id=9656, stack(0x00000002c9c00000,0x00000002c9d00000) (1024K)]
  0x00000149470ddb70 JavaThread "Unconstrained build operations Thread 103"        [_thread_blocked, id=16984, stack(0x00000002c9d00000,0x00000002c9e00000) (1024K)]
  0x0000014941590010 JavaThread "Unconstrained build operations Thread 104"        [_thread_blocked, id=18104, stack(0x00000002c9e00000,0x00000002c9f00000) (1024K)]
  0x0000014941595bf0 JavaThread "Unconstrained build operations Thread 105"        [_thread_blocked, id=8756, stack(0x00000002c9f00000,0x00000002ca000000) (1024K)]
  0x0000014941592e00 JavaThread "Unconstrained build operations Thread 106"        [_thread_blocked, id=20856, stack(0x00000002ca000000,0x00000002ca100000) (1024K)]
  0x0000014941596910 JavaThread "Unconstrained build operations Thread 107"        [_thread_blocked, id=13068, stack(0x00000002ca100000,0x00000002ca200000) (1024K)]
  0x0000014941591a50 JavaThread "Unconstrained build operations Thread 108"        [_thread_blocked, id=10380, stack(0x00000002ca200000,0x00000002ca300000) (1024K)]
  0x000001494158f980 JavaThread "Unconstrained build operations Thread 109"        [_thread_blocked, id=3180, stack(0x00000002ca300000,0x00000002ca400000) (1024K)]
  0x0000014941590d30 JavaThread "Unconstrained build operations Thread 110"        [_thread_blocked, id=7756, stack(0x00000002ca400000,0x00000002ca500000) (1024K)]
  0x0000014941595560 JavaThread "Unconstrained build operations Thread 111"        [_thread_blocked, id=16340, stack(0x00000002ca500000,0x00000002ca600000) (1024K)]
  0x0000014941593490 JavaThread "Unconstrained build operations Thread 112"        [_thread_blocked, id=12848, stack(0x00000002ca600000,0x00000002ca700000) (1024K)]
  0x0000014941593b20 JavaThread "Unconstrained build operations Thread 113"        [_thread_blocked, id=5548, stack(0x00000002ca700000,0x00000002ca800000) (1024K)]
  0x0000014941597630 JavaThread "Unconstrained build operations Thread 114"        [_thread_blocked, id=17556, stack(0x00000002ca800000,0x00000002ca900000) (1024K)]
  0x0000014941592770 JavaThread "Unconstrained build operations Thread 115"        [_thread_blocked, id=14864, stack(0x00000002ca900000,0x00000002caa00000) (1024K)]
  0x00000149415941b0 JavaThread "Unconstrained build operations Thread 116"        [_thread_blocked, id=16852, stack(0x00000002caa00000,0x00000002cab00000) (1024K)]
  0x00000149415906a0 JavaThread "Unconstrained build operations Thread 117"        [_thread_blocked, id=1320, stack(0x00000002cab00000,0x00000002cac00000) (1024K)]
  0x0000014941596280 JavaThread "Unconstrained build operations Thread 118"        [_thread_blocked, id=10184, stack(0x00000002cac00000,0x00000002cad00000) (1024K)]
  0x0000014941597cc0 JavaThread "Unconstrained build operations Thread 119"        [_thread_blocked, id=18276, stack(0x00000002cad00000,0x00000002cae00000) (1024K)]
  0x0000014941594840 JavaThread "Unconstrained build operations Thread 120"        [_thread_blocked, id=11240, stack(0x00000002cae00000,0x00000002caf00000) (1024K)]
  0x0000014941594ed0 JavaThread "Unconstrained build operations Thread 121"        [_thread_blocked, id=7608, stack(0x00000002caf00000,0x00000002cb000000) (1024K)]
  0x0000014941596fa0 JavaThread "Unconstrained build operations Thread 122"        [_thread_blocked, id=7816, stack(0x00000002cb000000,0x00000002cb100000) (1024K)]
  0x0000014941598350 JavaThread "Unconstrained build operations Thread 123"        [_thread_blocked, id=14140, stack(0x00000002cb100000,0x00000002cb200000) (1024K)]
  0x00000149415913c0 JavaThread "Unconstrained build operations Thread 124"        [_thread_blocked, id=11928, stack(0x00000002cb200000,0x00000002cb300000) (1024K)]
  0x00000149415989e0 JavaThread "Unconstrained build operations Thread 125"        [_thread_blocked, id=11444, stack(0x00000002cb300000,0x00000002cb400000) (1024K)]
  0x0000014941599070 JavaThread "Unconstrained build operations Thread 126"        [_thread_blocked, id=20348, stack(0x00000002cb400000,0x00000002cb500000) (1024K)]
  0x00000149415920e0 JavaThread "Unconstrained build operations Thread 127"        [_thread_blocked, id=20352, stack(0x00000002cb500000,0x00000002cb600000) (1024K)]
  0x0000014941599700 JavaThread "Unconstrained build operations Thread 128"        [_thread_blocked, id=13912, stack(0x00000002cb600000,0x00000002cb700000) (1024K)]
  0x000001494159b140 JavaThread "Unconstrained build operations Thread 129"        [_thread_blocked, id=15788, stack(0x00000002cb700000,0x00000002cb800000) (1024K)]
  0x000001494159c4f0 JavaThread "Unconstrained build operations Thread 130"        [_thread_blocked, id=18444, stack(0x00000002cb800000,0x00000002cb900000) (1024K)]
  0x000001494159cb80 JavaThread "Unconstrained build operations Thread 131"        [_thread_blocked, id=21364, stack(0x00000002cb900000,0x00000002cba00000) (1024K)]
  0x000001494159be60 JavaThread "Unconstrained build operations Thread 132"        [_thread_blocked, id=16784, stack(0x00000002cba00000,0x00000002cbb00000) (1024K)]
  0x000001494159e5c0 JavaThread "Unconstrained build operations Thread 133"        [_thread_blocked, id=15292, stack(0x00000002cbb00000,0x00000002cbc00000) (1024K)]
  0x000001494159df30 JavaThread "Unconstrained build operations Thread 134"        [_thread_blocked, id=18040, stack(0x00000002cbc00000,0x00000002cbd00000) (1024K)]
  0x000001494159ec50 JavaThread "Unconstrained build operations Thread 135"        [_thread_blocked, id=14132, stack(0x00000002cbd00000,0x00000002cbe00000) (1024K)]
  0x000001494159d210 JavaThread "Unconstrained build operations Thread 136"        [_thread_blocked, id=15524, stack(0x00000002cbe00000,0x00000002cbf00000) (1024K)]
  0x000001494159d8a0 JavaThread "Unconstrained build operations Thread 137"        [_thread_blocked, id=15240, stack(0x00000002cbf00000,0x00000002cc000000) (1024K)]
  0x00000149440e6240 JavaThread "build event listener"              [_thread_blocked, id=6976, stack(0x00000002cc000000,0x00000002cc100000) (1024K)]
  0x00000149440ebe20 JavaThread "Problems report writer"            [_thread_blocked, id=16224, stack(0x00000002cc200000,0x00000002cc300000) (1024K)]
  0x00000149440ecb40 JavaThread "jar transforms"                    [_thread_blocked, id=20692, stack(0x00000002c1b00000,0x00000002c1c00000) (1024K)]
  0x00000149440ed860 JavaThread "Unconstrained build operations Thread 138"        [_thread_blocked, id=20712, stack(0x00000002cc100000,0x00000002cc200000) (1024K)]
  0x00000149440eec10 JavaThread "Unconstrained build operations Thread 139"        [_thread_blocked, id=21424, stack(0x00000002cc300000,0x00000002cc400000) (1024K)]
  0x00000149440edef0 JavaThread "Unconstrained build operations Thread 140"        [_thread_blocked, id=7004, stack(0x00000002cc400000,0x00000002cc500000) (1024K)]
  0x00000149440ef2a0 JavaThread "Unconstrained build operations Thread 141"        [_thread_blocked, id=1360, stack(0x00000002cc500000,0x00000002cc600000) (1024K)]
  0x0000014945873810 JavaThread "Unconstrained build operations Thread 142"        [_thread_blocked, id=20280, stack(0x00000002cc600000,0x00000002cc700000) (1024K)]
  0x0000014943d3dba0 JavaThread "Unconstrained build operations Thread 143"        [_thread_blocked, id=14832, stack(0x00000002cc700000,0x00000002cc800000) (1024K)]
  0x0000014943d3e8c0 JavaThread "Unconstrained build operations Thread 144"        [_thread_blocked, id=9864, stack(0x00000002cc800000,0x00000002cc900000) (1024K)]
  0x0000014943d39a00 JavaThread "Unconstrained build operations Thread 145"        [_thread_blocked, id=17648, stack(0x00000002cc900000,0x00000002cca00000) (1024K)]
  0x0000014943d3fc70 JavaThread "Unconstrained build operations Thread 146"        [_thread_blocked, id=17452, stack(0x00000002cca00000,0x00000002ccb00000) (1024K)]
  0x0000014943d3e230 JavaThread "Unconstrained build operations Thread 147"        [_thread_blocked, id=20436, stack(0x00000002ccb00000,0x00000002ccc00000) (1024K)]
  0x0000014943d3a720 JavaThread "Unconstrained build operations Thread 148"        [_thread_blocked, id=7280, stack(0x00000002ccc00000,0x00000002ccd00000) (1024K)]
  0x0000014943d3b440 JavaThread "Unconstrained build operations Thread 149"        [_thread_blocked, id=3868, stack(0x00000002ccd00000,0x00000002cce00000) (1024K)]
  0x0000014943d3ce80 JavaThread "Unconstrained build operations Thread 150"        [_thread_blocked, id=9968, stack(0x00000002cce00000,0x00000002ccf00000) (1024K)]
  0x0000014943d3ef50 JavaThread "Unconstrained build operations Thread 151"        [_thread_blocked, id=16952, stack(0x00000002ccf00000,0x00000002cd000000) (1024K)]
  0x0000014943d3f5e0 JavaThread "Unconstrained build operations Thread 152"        [_thread_blocked, id=19176, stack(0x00000002cd000000,0x00000002cd100000) (1024K)]
  0x0000014943d39370 JavaThread "Unconstrained build operations Thread 153"        [_thread_blocked, id=15296, stack(0x00000002cd100000,0x00000002cd200000) (1024K)]
  0x0000014943d40990 JavaThread "Unconstrained build operations Thread 154"        [_thread_blocked, id=6056, stack(0x00000002cd200000,0x00000002cd300000) (1024K)]
  0x0000014943d41020 JavaThread "Unconstrained build operations Thread 155"        [_thread_blocked, id=9152, stack(0x00000002cd300000,0x00000002cd400000) (1024K)]
  0x0000014943d3c7f0 JavaThread "Unconstrained build operations Thread 156"        [_thread_blocked, id=18484, stack(0x00000002cd400000,0x00000002cd500000) (1024K)]
  0x0000014943d40300 JavaThread "Unconstrained build operations Thread 157"        [_thread_blocked, id=14464, stack(0x00000002cd500000,0x00000002cd600000) (1024K)]
  0x0000014943d3bad0 JavaThread "Unconstrained build operations Thread 158"        [_thread_blocked, id=16772, stack(0x00000002cd600000,0x00000002cd700000) (1024K)]
  0x0000014943d3a090 JavaThread "Unconstrained build operations Thread 159"        [_thread_blocked, id=7560, stack(0x00000002cd700000,0x00000002cd800000) (1024K)]
  0x0000014943d416b0 JavaThread "Unconstrained build operations Thread 160"        [_thread_blocked, id=14588, stack(0x00000002cd800000,0x00000002cd900000) (1024K)]
  0x0000014943d3d510 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=15936, stack(0x00000002cdb00000,0x00000002cdc00000) (1024K)]
  0x0000014943d444a0 JavaThread "pool-3-thread-1"                   [_thread_blocked, id=20956, stack(0x00000002c5100000,0x00000002c5200000) (1024K)]
  0x0000014943d44b30 JavaThread "build event listener"              [_thread_blocked, id=19828, stack(0x00000002cd900000,0x00000002cda00000) (1024K)]
  0x0000014943d46c00 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\Desktop\SportsApp project\sportsapp\android\.gradle\8.12\executionHistory)"        [_thread_blocked, id=13652, stack(0x00000002cda00000,0x00000002cdb00000) (1024K)]
  0x00000149440ea3e0 JavaThread "WorkerExecutor Queue"              [_thread_blocked, id=3396, stack(0x00000002ce200000,0x00000002ce300000) (1024K)]
  0x00000149456fecc0 JavaThread "WorkerExecutor Queue Thread 2"        [_thread_blocked, id=7516, stack(0x00000002ce300000,0x00000002ce400000) (1024K)]
  0x00000149456fab20 JavaThread "WorkerExecutor Queue Thread 3"        [_thread_in_native, id=18148, stack(0x00000002cde00000,0x00000002cdf00000) (1024K)]
  0x00000149456fc560 JavaThread "RMI Scheduler(0)"           daemon [_thread_blocked, id=12212, stack(0x00000002ce000000,0x00000002ce100000) (1024K)]
  0x00000149456ff350 JavaThread "RMI RenewClean-[127.0.0.1:17816,org.jetbrains.kotlin.daemon.common.LoopbackNetworkInterface$ClientLoopbackSocketFactory@13117ff6]" daemon [_thread_blocked, id=19744, stack(0x00000002ce100000,0x00000002ce200000) (1024K)]
  0x00000149456fb1b0 JavaThread "RMI GC Daemon"              daemon [_thread_blocked, id=11880, stack(0x00000002ce400000,0x00000002ce500000) (1024K)]
  0x00000149456f83c0 JavaThread "RMI TCP Accept-0"           daemon [_thread_in_native, id=14352, stack(0x00000002cdf00000,0x00000002ce000000) (1024K)]
  0x00000149456fd280 JavaThread "RMI Reaper"                        [_thread_blocked, id=13104, stack(0x00000002ce500000,0x00000002ce600000) (1024K)]
  0x00000149456fb840 JavaThread "RMI TCP Connection(idle)"   daemon [_thread_blocked, id=20088, stack(0x00000002ce800000,0x00000002ce900000) (1024K)]
  0x00000149412d9f70 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=12972, stack(0x00000002cdc00000,0x00000002cdd00000) (1024K)]
  0x00000149412da640 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=17616, stack(0x00000002cdd00000,0x00000002cde00000) (1024K)]
  0x00000149412d91d0 JavaThread "C2 CompilerThread3"         daemon [_thread_blocked, id=7388, stack(0x00000002ce900000,0x00000002cea00000) (1024K)]
  0x00000149412dbab0 JavaThread "C1 CompilerThread1"         daemon [_thread_blocked, id=15108, stack(0x00000002bfa00000,0x00000002bfb00000) (1024K)]
  0x00000149502de790 JavaThread "C1 CompilerThread2"         daemon [_thread_blocked, id=13808, stack(0x00000002cea00000,0x00000002ceb00000) (1024K)]
Total: 233

Other Threads:
=>0x000001493b154870 VMThread "VM Thread"                           [id=19632, stack(0x00000002bf000000,0x00000002bf100000) (1024K)]
  0x000001493b11cf80 WatcherThread "VM Periodic Task Thread"        [id=20176, stack(0x00000002bef00000,0x00000002bf000000) (1024K)]
  0x000001490aef71f0 WorkerThread "GC Thread#0"                     [id=18592, stack(0x00000002bea00000,0x00000002beb00000) (1024K)]
  0x00000149404d8550 WorkerThread "GC Thread#1"                     [id=12012, stack(0x00000002bff00000,0x00000002c0000000) (1024K)]
  0x00000149404d88f0 WorkerThread "GC Thread#2"                     [id=1512, stack(0x00000002c0000000,0x00000002c0100000) (1024K)]
  0x000001494063dc40 WorkerThread "GC Thread#3"                     [id=17456, stack(0x00000002c0100000,0x00000002c0200000) (1024K)]
  0x000001494063dfe0 WorkerThread "GC Thread#4"                     [id=16920, stack(0x00000002c0200000,0x00000002c0300000) (1024K)]
  0x0000014942038980 WorkerThread "GC Thread#5"                     [id=15512, stack(0x00000002c0600000,0x00000002c0700000) (1024K)]
  0x0000014942039ba0 WorkerThread "GC Thread#6"                     [id=11188, stack(0x00000002c0700000,0x00000002c0800000) (1024K)]
  0x0000014942039800 WorkerThread "GC Thread#7"                     [id=19608, stack(0x00000002c0800000,0x00000002c0900000) (1024K)]
  0x0000014942039f40 WorkerThread "GC Thread#8"                     [id=16840, stack(0x00000002c0900000,0x00000002c0a00000) (1024K)]
  0x00000149420390c0 WorkerThread "GC Thread#9"                     [id=13196, stack(0x00000002c0a00000,0x00000002c0b00000) (1024K)]
  0x00000149420385e0 WorkerThread "GC Thread#10"                    [id=6540, stack(0x00000002c0b00000,0x00000002c0c00000) (1024K)]
  0x0000014942039460 WorkerThread "GC Thread#11"                    [id=2604, stack(0x00000002c0c00000,0x00000002c0d00000) (1024K)]
  0x0000014942038d20 WorkerThread "GC Thread#12"                    [id=10796, stack(0x00000002c1500000,0x00000002c1600000) (1024K)]
  0x000001490af09350 ConcurrentGCThread "G1 Main Marker"            [id=7980, stack(0x00000002beb00000,0x00000002bec00000) (1024K)]
  0x000001490af0b670 WorkerThread "G1 Conc#0"                       [id=3788, stack(0x00000002bec00000,0x00000002bed00000) (1024K)]
  0x000001494215fba0 WorkerThread "G1 Conc#1"                       [id=5508, stack(0x00000002c1600000,0x00000002c1700000) (1024K)]
  0x0000014942160dc0 WorkerThread "G1 Conc#2"                       [id=12568, stack(0x00000002c1700000,0x00000002c1800000) (1024K)]
  0x000001493afefbf0 ConcurrentGCThread "G1 Refine#0"               [id=16600, stack(0x00000002bed00000,0x00000002bee00000) (1024K)]
  0x000001493aff0670 ConcurrentGCThread "G1 Service"                [id=13144, stack(0x00000002bee00000,0x00000002bef00000) (1024K)]
Total: 21

Threads with active compile tasks:
C2 CompilerThread0  45797 28248       4       com.android.tools.r8.internal.CQ::j (1048 bytes)
C2 CompilerThread1  45797 28228 %     4       com.android.tools.r8.internal.aA::d @ 256 (1116 bytes)
C2 CompilerThread2  45797 28047       4       com.android.tools.r8.graph.E2$$Lambda/0x00000008016a0000::accept (12 bytes)
C2 CompilerThread3  45797 28163       4       com.android.tools.r8.internal.vd::a (4363 bytes)
C1 CompilerThread2  45797 28293       3       com.android.tools.r8.internal.y7::c (2193 bytes)
Total: 5

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffd11b5e3a0] Threads_lock - owner thread: 0x000001493b154870
[0x00007ffd11b5e4a0] Heap_lock - owner thread: 0x000001494729cf50

Heap address: 0x0000000600000000, size: 8192 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 16 total, 16 available
 Memory: 15697M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 248M
 Heap Max Capacity: 8G
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 880640K, used 330623K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 12 young (49152K), 12 survivors (49152K)
 Metaspace       used 166494K, committed 168960K, reserved 1245184K
  class space    used 23057K, committed 24320K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000600000000, 0x0000000600400000, 0x0000000600400000|100%|HS|  |TAMS 0x0000000600400000| PB 0x0000000600000000| Complete 
|   1|0x0000000600400000, 0x0000000600800000, 0x0000000600800000|100%| O|  |TAMS 0x0000000600800000| PB 0x0000000600400000| Untracked 
|   2|0x0000000600800000, 0x0000000600c00000, 0x0000000600c00000|100%| O|  |TAMS 0x0000000600c00000| PB 0x0000000600800000| Untracked 
|   3|0x0000000600c00000, 0x0000000601000000, 0x0000000601000000|100%| O|  |TAMS 0x0000000601000000| PB 0x0000000600c00000| Untracked 
|   4|0x0000000601000000, 0x0000000601400000, 0x0000000601400000|100%| O|  |TAMS 0x0000000601400000| PB 0x0000000601000000| Untracked 
|   5|0x0000000601400000, 0x0000000601800000, 0x0000000601800000|100%| O|  |TAMS 0x0000000601800000| PB 0x0000000601400000| Untracked 
|   6|0x0000000601800000, 0x0000000601c00000, 0x0000000601c00000|100%| O|  |TAMS 0x0000000601c00000| PB 0x0000000601800000| Untracked 
|   7|0x0000000601c00000, 0x0000000602000000, 0x0000000602000000|100%| O|  |TAMS 0x0000000602000000| PB 0x0000000601c00000| Untracked 
|   8|0x0000000602000000, 0x0000000602400000, 0x0000000602400000|100%| O|  |TAMS 0x0000000602400000| PB 0x0000000602000000| Untracked 
|   9|0x0000000602400000, 0x0000000602800000, 0x0000000602800000|100%|HS|  |TAMS 0x0000000602800000| PB 0x0000000602400000| Complete 
|  10|0x0000000602800000, 0x0000000602c00000, 0x0000000602c00000|100%| O|  |TAMS 0x0000000602c00000| PB 0x0000000602800000| Untracked 
|  11|0x0000000602c00000, 0x0000000603000000, 0x0000000603000000|100%| O|  |TAMS 0x0000000603000000| PB 0x0000000602c00000| Untracked 
|  12|0x0000000603000000, 0x0000000603400000, 0x0000000603400000|100%|HS|  |TAMS 0x0000000603400000| PB 0x0000000603000000| Complete 
|  13|0x0000000603400000, 0x0000000603800000, 0x0000000603800000|100%| O|  |TAMS 0x0000000603800000| PB 0x0000000603400000| Untracked 
|  14|0x0000000603800000, 0x0000000603c00000, 0x0000000603c00000|100%| O|  |TAMS 0x0000000603c00000| PB 0x0000000603800000| Untracked 
|  15|0x0000000603c00000, 0x0000000604000000, 0x0000000604000000|100%| O|  |TAMS 0x0000000604000000| PB 0x0000000603c00000| Untracked 
|  16|0x0000000604000000, 0x0000000604400000, 0x0000000604400000|100%| O|  |TAMS 0x0000000604400000| PB 0x0000000604000000| Untracked 
|  17|0x0000000604400000, 0x0000000604800000, 0x0000000604800000|100%| O|  |TAMS 0x0000000604800000| PB 0x0000000604400000| Untracked 
|  18|0x0000000604800000, 0x0000000604c00000, 0x0000000604c00000|100%| O|  |TAMS 0x0000000604c00000| PB 0x0000000604800000| Untracked 
|  19|0x0000000604c00000, 0x0000000605000000, 0x0000000605000000|100%| O|  |TAMS 0x0000000605000000| PB 0x0000000604c00000| Untracked 
|  20|0x0000000605000000, 0x0000000605400000, 0x0000000605400000|100%| O|  |TAMS 0x0000000605400000| PB 0x0000000605000000| Untracked 
|  21|0x0000000605400000, 0x0000000605800000, 0x0000000605800000|100%| O|  |TAMS 0x0000000605800000| PB 0x0000000605400000| Untracked 
|  22|0x0000000605800000, 0x0000000605c00000, 0x0000000605c00000|100%| O|  |TAMS 0x0000000605c00000| PB 0x0000000605800000| Untracked 
|  23|0x0000000605c00000, 0x0000000606000000, 0x0000000606000000|100%| O|  |TAMS 0x0000000606000000| PB 0x0000000605c00000| Untracked 
|  24|0x0000000606000000, 0x0000000606400000, 0x0000000606400000|100%| O|  |TAMS 0x0000000606400000| PB 0x0000000606000000| Untracked 
|  25|0x0000000606400000, 0x0000000606800000, 0x0000000606800000|100%| O|  |TAMS 0x0000000606800000| PB 0x0000000606400000| Untracked 
|  26|0x0000000606800000, 0x0000000606c00000, 0x0000000606c00000|100%| O|  |TAMS 0x0000000606c00000| PB 0x0000000606800000| Untracked 
|  27|0x0000000606c00000, 0x0000000607000000, 0x0000000607000000|100%| O|  |TAMS 0x0000000607000000| PB 0x0000000606c00000| Untracked 
|  28|0x0000000607000000, 0x0000000607400000, 0x0000000607400000|100%| O|  |TAMS 0x0000000607400000| PB 0x0000000607000000| Untracked 
|  29|0x0000000607400000, 0x0000000607800000, 0x0000000607800000|100%| O|  |TAMS 0x0000000607800000| PB 0x0000000607400000| Untracked 
|  30|0x0000000607800000, 0x0000000607c00000, 0x0000000607c00000|100%| O|  |TAMS 0x0000000607c00000| PB 0x0000000607800000| Untracked 
|  31|0x0000000607c00000, 0x0000000608000000, 0x0000000608000000|100%| O|  |TAMS 0x0000000608000000| PB 0x0000000607c00000| Untracked 
|  32|0x0000000608000000, 0x0000000608400000, 0x0000000608400000|100%| O|  |TAMS 0x0000000608400000| PB 0x0000000608000000| Untracked 
|  33|0x0000000608400000, 0x0000000608800000, 0x0000000608800000|100%| O|  |TAMS 0x0000000608800000| PB 0x0000000608400000| Untracked 
|  34|0x0000000608800000, 0x0000000608c00000, 0x0000000608c00000|100%| O|  |TAMS 0x0000000608c00000| PB 0x0000000608800000| Untracked 
|  35|0x0000000608c00000, 0x0000000609000000, 0x0000000609000000|100%| O|  |TAMS 0x0000000609000000| PB 0x0000000608c00000| Untracked 
|  36|0x0000000609000000, 0x0000000609400000, 0x0000000609400000|100%| O|  |TAMS 0x0000000609400000| PB 0x0000000609000000| Untracked 
|  37|0x0000000609400000, 0x0000000609800000, 0x0000000609800000|100%| O|  |TAMS 0x0000000609800000| PB 0x0000000609400000| Untracked 
|  38|0x0000000609800000, 0x0000000609c00000, 0x0000000609c00000|100%| O|  |TAMS 0x0000000609c00000| PB 0x0000000609800000| Untracked 
|  39|0x0000000609c00000, 0x000000060a000000, 0x000000060a000000|100%| O|  |TAMS 0x000000060a000000| PB 0x0000000609c00000| Untracked 
|  40|0x000000060a000000, 0x000000060a400000, 0x000000060a400000|100%| O|  |TAMS 0x000000060a400000| PB 0x000000060a000000| Untracked 
|  41|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  42|0x000000060a800000, 0x000000060ac00000, 0x000000060ac00000|100%| O|  |TAMS 0x000000060ac00000| PB 0x000000060a800000| Untracked 
|  43|0x000000060ac00000, 0x000000060b000000, 0x000000060b000000|100%| O|  |TAMS 0x000000060b000000| PB 0x000000060ac00000| Untracked 
|  44|0x000000060b000000, 0x000000060b400000, 0x000000060b400000|100%| O|  |TAMS 0x000000060b400000| PB 0x000000060b000000| Untracked 
|  45|0x000000060b400000, 0x000000060b800000, 0x000000060b800000|100%| O|  |TAMS 0x000000060b800000| PB 0x000000060b400000| Untracked 
|  46|0x000000060b800000, 0x000000060bc00000, 0x000000060bc00000|100%| O|  |TAMS 0x000000060bc00000| PB 0x000000060b800000| Untracked 
|  47|0x000000060bc00000, 0x000000060c000000, 0x000000060c000000|100%| O|  |TAMS 0x000000060c000000| PB 0x000000060bc00000| Untracked 
|  48|0x000000060c000000, 0x000000060c400000, 0x000000060c400000|100%| O|  |TAMS 0x000000060c400000| PB 0x000000060c000000| Untracked 
|  49|0x000000060c400000, 0x000000060c800000, 0x000000060c800000|100%| O|  |TAMS 0x000000060c800000| PB 0x000000060c400000| Untracked 
|  50|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000| PB 0x000000060c800000| Untracked 
|  51|0x000000060cc00000, 0x000000060d000000, 0x000000060d000000|100%| O|  |TAMS 0x000000060d000000| PB 0x000000060cc00000| Untracked 
|  52|0x000000060d000000, 0x000000060d400000, 0x000000060d400000|100%| O|  |TAMS 0x000000060d400000| PB 0x000000060d000000| Untracked 
|  53|0x000000060d400000, 0x000000060d800000, 0x000000060d800000|100%| O|  |TAMS 0x000000060d800000| PB 0x000000060d400000| Untracked 
|  54|0x000000060d800000, 0x000000060dc00000, 0x000000060dc00000|100%| O|  |TAMS 0x000000060dc00000| PB 0x000000060d800000| Untracked 
|  55|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000| PB 0x000000060dc00000| Untracked 
|  56|0x000000060e000000, 0x000000060e400000, 0x000000060e400000|100%| O|  |TAMS 0x000000060e400000| PB 0x000000060e000000| Untracked 
|  57|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  58|0x000000060e800000, 0x000000060ec00000, 0x000000060ec00000|100%| O|  |TAMS 0x000000060ec00000| PB 0x000000060e800000| Untracked 
|  59|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|  60|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|  61|0x000000060f400000, 0x000000060f800000, 0x000000060f800000|100%| O|  |TAMS 0x000000060f800000| PB 0x000000060f400000| Untracked 
|  62|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Untracked 
|  63|0x000000060fc00000, 0x0000000610000000, 0x0000000610000000|100%|HS|  |TAMS 0x0000000610000000| PB 0x000000060fc00000| Complete 
|  64|0x0000000610000000, 0x0000000610400000, 0x0000000610400000|100%|HC|  |TAMS 0x0000000610400000| PB 0x0000000610000000| Complete 
|  65|0x0000000610400000, 0x0000000610800000, 0x0000000610800000|100%| O|  |TAMS 0x0000000610800000| PB 0x0000000610400000| Untracked 
|  66|0x0000000610800000, 0x0000000610c00000, 0x0000000610c00000|100%| O|  |TAMS 0x0000000610c00000| PB 0x0000000610800000| Untracked 
|  67|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Untracked 
|  68|0x0000000611000000, 0x000000061133d7c8, 0x0000000611400000| 81%| S|CS|TAMS 0x0000000611000000| PB 0x0000000611000000| Complete 
|  69|0x0000000611400000, 0x0000000611800000, 0x0000000611800000|100%| S|CS|TAMS 0x0000000611400000| PB 0x0000000611400000| Complete 
|  70|0x0000000611800000, 0x0000000611c00000, 0x0000000611c00000|100%| O|  |TAMS 0x0000000611c00000| PB 0x0000000611800000| Untracked 
|  71|0x0000000611c00000, 0x0000000612000000, 0x0000000612000000|100%| O|  |TAMS 0x0000000612000000| PB 0x0000000611c00000| Untracked 
|  72|0x0000000612000000, 0x0000000612400000, 0x0000000612400000|100%| O|  |TAMS 0x0000000612400000| PB 0x0000000612000000| Untracked 
|  73|0x0000000612400000, 0x0000000612800000, 0x0000000612800000|100%| O|  |TAMS 0x0000000612800000| PB 0x0000000612400000| Untracked 
|  74|0x0000000612800000, 0x0000000612c00000, 0x0000000612c00000|100%| O|  |TAMS 0x0000000612c00000| PB 0x0000000612800000| Untracked 
|  75|0x0000000612c00000, 0x0000000613000000, 0x0000000613000000|100%| O|  |TAMS 0x0000000613000000| PB 0x0000000612c00000| Untracked 
|  76|0x0000000613000000, 0x0000000613400000, 0x0000000613400000|100%| O|  |TAMS 0x0000000613400000| PB 0x0000000613000000| Untracked 
|  77|0x0000000613400000, 0x00000006137a2710, 0x0000000613800000| 90%| O|  |TAMS 0x00000006137a2710| PB 0x0000000613400000| Untracked 
|  78|0x0000000613800000, 0x0000000613c00000, 0x0000000613c00000|100%| S|CS|TAMS 0x0000000613800000| PB 0x0000000613800000| Complete 
|  79|0x0000000613c00000, 0x0000000614000000, 0x0000000614000000|100%| S|CS|TAMS 0x0000000613c00000| PB 0x0000000613c00000| Complete 
|  80|0x0000000614000000, 0x0000000614400000, 0x0000000614400000|100%| S|CS|TAMS 0x0000000614000000| PB 0x0000000614000000| Complete 
|  81|0x0000000614400000, 0x0000000614800000, 0x0000000614800000|100%| S|CS|TAMS 0x0000000614400000| PB 0x0000000614400000| Complete 
|  82|0x0000000614800000, 0x0000000614c00000, 0x0000000614c00000|100%| S|CS|TAMS 0x0000000614800000| PB 0x0000000614800000| Complete 
|  83|0x0000000614c00000, 0x0000000615000000, 0x0000000615000000|100%| S|CS|TAMS 0x0000000614c00000| PB 0x0000000614c00000| Complete 
|  84|0x0000000615000000, 0x0000000615400000, 0x0000000615400000|100%| S|CS|TAMS 0x0000000615000000| PB 0x0000000615000000| Complete 
|  85|0x0000000615400000, 0x0000000615800000, 0x0000000615800000|100%| S|CS|TAMS 0x0000000615400000| PB 0x0000000615400000| Complete 
|  86|0x0000000615800000, 0x0000000615c00000, 0x0000000615c00000|100%| S|CS|TAMS 0x0000000615800000| PB 0x0000000615800000| Complete 
|  87|0x0000000615c00000, 0x0000000616000000, 0x0000000616000000|100%| S|CS|TAMS 0x0000000615c00000| PB 0x0000000615c00000| Complete 
|  88|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  89|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Untracked 
|  90|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Untracked 
|  91|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Untracked 
|  92|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Untracked 
|  93|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Untracked 
|  94|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Untracked 
|  95|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Untracked 
|  96|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Untracked 
|  97|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  98|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  99|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
| 100|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
| 101|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
| 102|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
| 103|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Untracked 
| 104|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Untracked 
| 105|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Untracked 
| 106|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Untracked 
| 107|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Untracked 
| 108|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Untracked 
| 109|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Untracked 
| 110|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Untracked 
| 111|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Untracked 
| 112|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Untracked 
| 113|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
| 114|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
| 115|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
| 116|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
| 117|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Untracked 
| 118|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Untracked 
| 119|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Untracked 
| 120|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Untracked 
| 121|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Untracked 
| 122|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
| 123|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Untracked 
| 124|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000| PB 0x000000061f000000| Untracked 
| 125|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000| PB 0x000000061f400000| Untracked 
| 126|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000| PB 0x000000061f800000| Untracked 
| 127|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000| PB 0x000000061fc00000| Untracked 
| 128|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000| PB 0x0000000620000000| Untracked 
| 129|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000| PB 0x0000000620400000| Untracked 
| 130|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000| PB 0x0000000620800000| Untracked 
| 131|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000| PB 0x0000000620c00000| Untracked 
| 132|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000| PB 0x0000000621000000| Untracked 
| 133|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000| PB 0x0000000621400000| Untracked 
| 134|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000| PB 0x0000000621800000| Untracked 
| 135|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000| PB 0x0000000621c00000| Untracked 
| 136|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000| PB 0x0000000622000000| Untracked 
| 137|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000| PB 0x0000000622400000| Untracked 
| 138|0x0000000622800000, 0x0000000622800000, 0x0000000622c00000|  0%| F|  |TAMS 0x0000000622800000| PB 0x0000000622800000| Untracked 
| 139|0x0000000622c00000, 0x0000000622c00000, 0x0000000623000000|  0%| F|  |TAMS 0x0000000622c00000| PB 0x0000000622c00000| Untracked 
| 140|0x0000000623000000, 0x0000000623000000, 0x0000000623400000|  0%| F|  |TAMS 0x0000000623000000| PB 0x0000000623000000| Untracked 
| 141|0x0000000623400000, 0x0000000623400000, 0x0000000623800000|  0%| F|  |TAMS 0x0000000623400000| PB 0x0000000623400000| Untracked 
| 142|0x0000000623800000, 0x0000000623800000, 0x0000000623c00000|  0%| F|  |TAMS 0x0000000623800000| PB 0x0000000623800000| Untracked 
| 143|0x0000000623c00000, 0x0000000623c00000, 0x0000000624000000|  0%| F|  |TAMS 0x0000000623c00000| PB 0x0000000623c00000| Untracked 
| 144|0x0000000624000000, 0x0000000624000000, 0x0000000624400000|  0%| F|  |TAMS 0x0000000624000000| PB 0x0000000624000000| Untracked 
| 145|0x0000000624400000, 0x0000000624400000, 0x0000000624800000|  0%| F|  |TAMS 0x0000000624400000| PB 0x0000000624400000| Untracked 
| 146|0x0000000624800000, 0x0000000624800000, 0x0000000624c00000|  0%| F|  |TAMS 0x0000000624800000| PB 0x0000000624800000| Untracked 
| 147|0x0000000624c00000, 0x0000000624c00000, 0x0000000625000000|  0%| F|  |TAMS 0x0000000624c00000| PB 0x0000000624c00000| Untracked 
| 148|0x0000000625000000, 0x0000000625000000, 0x0000000625400000|  0%| F|  |TAMS 0x0000000625000000| PB 0x0000000625000000| Untracked 
| 149|0x0000000625400000, 0x0000000625400000, 0x0000000625800000|  0%| F|  |TAMS 0x0000000625400000| PB 0x0000000625400000| Untracked 
| 150|0x0000000625800000, 0x0000000625800000, 0x0000000625c00000|  0%| F|  |TAMS 0x0000000625800000| PB 0x0000000625800000| Untracked 
| 151|0x0000000625c00000, 0x0000000625c00000, 0x0000000626000000|  0%| F|  |TAMS 0x0000000625c00000| PB 0x0000000625c00000| Untracked 
| 152|0x0000000626000000, 0x0000000626000000, 0x0000000626400000|  0%| F|  |TAMS 0x0000000626000000| PB 0x0000000626000000| Untracked 
| 153|0x0000000626400000, 0x0000000626400000, 0x0000000626800000|  0%| F|  |TAMS 0x0000000626400000| PB 0x0000000626400000| Untracked 
| 154|0x0000000626800000, 0x0000000626800000, 0x0000000626c00000|  0%| F|  |TAMS 0x0000000626800000| PB 0x0000000626800000| Untracked 
| 155|0x0000000626c00000, 0x0000000626c00000, 0x0000000627000000|  0%| F|  |TAMS 0x0000000626c00000| PB 0x0000000626c00000| Untracked 
| 156|0x0000000627000000, 0x0000000627000000, 0x0000000627400000|  0%| F|  |TAMS 0x0000000627000000| PB 0x0000000627000000| Untracked 
| 157|0x0000000627400000, 0x0000000627400000, 0x0000000627800000|  0%| F|  |TAMS 0x0000000627400000| PB 0x0000000627400000| Untracked 
| 158|0x0000000627800000, 0x0000000627800000, 0x0000000627c00000|  0%| F|  |TAMS 0x0000000627800000| PB 0x0000000627800000| Untracked 
| 159|0x0000000627c00000, 0x0000000627c00000, 0x0000000628000000|  0%| F|  |TAMS 0x0000000627c00000| PB 0x0000000627c00000| Untracked 
| 160|0x0000000628000000, 0x0000000628000000, 0x0000000628400000|  0%| F|  |TAMS 0x0000000628000000| PB 0x0000000628000000| Untracked 
| 161|0x0000000628400000, 0x0000000628400000, 0x0000000628800000|  0%| F|  |TAMS 0x0000000628400000| PB 0x0000000628400000| Untracked 
| 162|0x0000000628800000, 0x0000000628800000, 0x0000000628c00000|  0%| F|  |TAMS 0x0000000628800000| PB 0x0000000628800000| Untracked 
| 163|0x0000000628c00000, 0x0000000628c00000, 0x0000000629000000|  0%| F|  |TAMS 0x0000000628c00000| PB 0x0000000628c00000| Untracked 
| 164|0x0000000629000000, 0x0000000629000000, 0x0000000629400000|  0%| F|  |TAMS 0x0000000629000000| PB 0x0000000629000000| Untracked 
| 165|0x0000000629400000, 0x0000000629400000, 0x0000000629800000|  0%| F|  |TAMS 0x0000000629400000| PB 0x0000000629400000| Untracked 
| 166|0x0000000629800000, 0x0000000629800000, 0x0000000629c00000|  0%| F|  |TAMS 0x0000000629800000| PB 0x0000000629800000| Untracked 
| 167|0x0000000629c00000, 0x0000000629c00000, 0x000000062a000000|  0%| F|  |TAMS 0x0000000629c00000| PB 0x0000000629c00000| Untracked 
| 168|0x000000062a000000, 0x000000062a000000, 0x000000062a400000|  0%| F|  |TAMS 0x000000062a000000| PB 0x000000062a000000| Untracked 
| 169|0x000000062a400000, 0x000000062a400000, 0x000000062a800000|  0%| F|  |TAMS 0x000000062a400000| PB 0x000000062a400000| Untracked 
| 170|0x000000062a800000, 0x000000062a800000, 0x000000062ac00000|  0%| F|  |TAMS 0x000000062a800000| PB 0x000000062a800000| Untracked 
| 171|0x000000062ac00000, 0x000000062ac00000, 0x000000062b000000|  0%| F|  |TAMS 0x000000062ac00000| PB 0x000000062ac00000| Untracked 
| 172|0x000000062b000000, 0x000000062b000000, 0x000000062b400000|  0%| F|  |TAMS 0x000000062b000000| PB 0x000000062b000000| Untracked 
| 173|0x000000062b400000, 0x000000062b400000, 0x000000062b800000|  0%| F|  |TAMS 0x000000062b400000| PB 0x000000062b400000| Untracked 
| 174|0x000000062b800000, 0x000000062b800000, 0x000000062bc00000|  0%| F|  |TAMS 0x000000062b800000| PB 0x000000062b800000| Untracked 
| 175|0x000000062bc00000, 0x000000062bc00000, 0x000000062c000000|  0%| F|  |TAMS 0x000000062bc00000| PB 0x000000062bc00000| Untracked 
| 176|0x000000062c000000, 0x000000062c000000, 0x000000062c400000|  0%| F|  |TAMS 0x000000062c000000| PB 0x000000062c000000| Untracked 
| 177|0x000000062c400000, 0x000000062c400000, 0x000000062c800000|  0%| F|  |TAMS 0x000000062c400000| PB 0x000000062c400000| Untracked 
| 178|0x000000062c800000, 0x000000062c800000, 0x000000062cc00000|  0%| F|  |TAMS 0x000000062c800000| PB 0x000000062c800000| Untracked 
| 179|0x000000062cc00000, 0x000000062cc00000, 0x000000062d000000|  0%| F|  |TAMS 0x000000062cc00000| PB 0x000000062cc00000| Untracked 
| 180|0x000000062d000000, 0x000000062d000000, 0x000000062d400000|  0%| F|  |TAMS 0x000000062d000000| PB 0x000000062d000000| Untracked 
| 181|0x000000062d400000, 0x000000062d400000, 0x000000062d800000|  0%| F|  |TAMS 0x000000062d400000| PB 0x000000062d400000| Untracked 
| 182|0x000000062d800000, 0x000000062d800000, 0x000000062dc00000|  0%| F|  |TAMS 0x000000062d800000| PB 0x000000062d800000| Untracked 
| 183|0x000000062dc00000, 0x000000062dc00000, 0x000000062e000000|  0%| F|  |TAMS 0x000000062dc00000| PB 0x000000062dc00000| Untracked 
| 184|0x000000062e000000, 0x000000062e000000, 0x000000062e400000|  0%| F|  |TAMS 0x000000062e000000| PB 0x000000062e000000| Untracked 
| 185|0x000000062e400000, 0x000000062e400000, 0x000000062e800000|  0%| F|  |TAMS 0x000000062e400000| PB 0x000000062e400000| Untracked 
| 186|0x000000062e800000, 0x000000062e800000, 0x000000062ec00000|  0%| F|  |TAMS 0x000000062e800000| PB 0x000000062e800000| Untracked 
| 187|0x000000062ec00000, 0x000000062ec00000, 0x000000062f000000|  0%| F|  |TAMS 0x000000062ec00000| PB 0x000000062ec00000| Untracked 
| 188|0x000000062f000000, 0x000000062f000000, 0x000000062f400000|  0%| F|  |TAMS 0x000000062f000000| PB 0x000000062f000000| Untracked 
| 189|0x000000062f400000, 0x000000062f400000, 0x000000062f800000|  0%| F|  |TAMS 0x000000062f400000| PB 0x000000062f400000| Untracked 
| 190|0x000000062f800000, 0x000000062f800000, 0x000000062fc00000|  0%| F|  |TAMS 0x000000062f800000| PB 0x000000062f800000| Untracked 
| 191|0x000000062fc00000, 0x000000062fc00000, 0x0000000630000000|  0%| F|  |TAMS 0x000000062fc00000| PB 0x000000062fc00000| Untracked 
| 192|0x0000000630000000, 0x0000000630000000, 0x0000000630400000|  0%| F|  |TAMS 0x0000000630000000| PB 0x0000000630000000| Untracked 
| 193|0x0000000630400000, 0x0000000630400000, 0x0000000630800000|  0%| F|  |TAMS 0x0000000630400000| PB 0x0000000630400000| Untracked 
| 194|0x0000000630800000, 0x0000000630800000, 0x0000000630c00000|  0%| F|  |TAMS 0x0000000630800000| PB 0x0000000630800000| Untracked 
| 195|0x0000000630c00000, 0x0000000630c00000, 0x0000000631000000|  0%| F|  |TAMS 0x0000000630c00000| PB 0x0000000630c00000| Untracked 
| 196|0x0000000631000000, 0x0000000631000000, 0x0000000631400000|  0%| F|  |TAMS 0x0000000631000000| PB 0x0000000631000000| Untracked 
| 197|0x0000000631400000, 0x0000000631400000, 0x0000000631800000|  0%| F|  |TAMS 0x0000000631400000| PB 0x0000000631400000| Untracked 
| 198|0x0000000631800000, 0x0000000631800000, 0x0000000631c00000|  0%| F|  |TAMS 0x0000000631800000| PB 0x0000000631800000| Untracked 
| 199|0x0000000631c00000, 0x0000000631c00000, 0x0000000632000000|  0%| F|  |TAMS 0x0000000631c00000| PB 0x0000000631c00000| Untracked 
| 200|0x0000000632000000, 0x0000000632000000, 0x0000000632400000|  0%| F|  |TAMS 0x0000000632000000| PB 0x0000000632000000| Untracked 
| 201|0x0000000632400000, 0x0000000632400000, 0x0000000632800000|  0%| F|  |TAMS 0x0000000632400000| PB 0x0000000632400000| Untracked 
| 202|0x0000000632800000, 0x0000000632800000, 0x0000000632c00000|  0%| F|  |TAMS 0x0000000632800000| PB 0x0000000632800000| Untracked 
| 203|0x0000000632c00000, 0x0000000632c00000, 0x0000000633000000|  0%| F|  |TAMS 0x0000000632c00000| PB 0x0000000632c00000| Untracked 
| 204|0x0000000633000000, 0x0000000633000000, 0x0000000633400000|  0%| F|  |TAMS 0x0000000633000000| PB 0x0000000633000000| Untracked 
| 205|0x0000000633400000, 0x0000000633400000, 0x0000000633800000|  0%| F|  |TAMS 0x0000000633400000| PB 0x0000000633400000| Untracked 
| 206|0x0000000633800000, 0x0000000633800000, 0x0000000633c00000|  0%| F|  |TAMS 0x0000000633800000| PB 0x0000000633800000| Untracked 
| 207|0x0000000633c00000, 0x0000000633c00000, 0x0000000634000000|  0%| F|  |TAMS 0x0000000633c00000| PB 0x0000000633c00000| Untracked 
| 208|0x0000000634000000, 0x0000000634000000, 0x0000000634400000|  0%| F|  |TAMS 0x0000000634000000| PB 0x0000000634000000| Untracked 
| 209|0x0000000634400000, 0x0000000634400000, 0x0000000634800000|  0%| F|  |TAMS 0x0000000634400000| PB 0x0000000634400000| Untracked 
| 210|0x0000000634800000, 0x0000000634800000, 0x0000000634c00000|  0%| F|  |TAMS 0x0000000634800000| PB 0x0000000634800000| Untracked 
| 211|0x0000000634c00000, 0x0000000634c00000, 0x0000000635000000|  0%| F|  |TAMS 0x0000000634c00000| PB 0x0000000634c00000| Untracked 
|2045|0x00000007ff400000, 0x00000007ff400000, 0x00000007ff800000|  0%| F|  |TAMS 0x00000007ff400000| PB 0x00000007ff400000| Untracked 
|2046|0x00000007ff800000, 0x00000007ffc00000, 0x00000007ffc00000|100%| O|  |TAMS 0x00000007ffc00000| PB 0x00000007ff800000| Untracked 
|2047|0x00000007ffc00000, 0x00000007ffc00000, 0x0000000800000000|  0%| F|  |TAMS 0x00000007ffc00000| PB 0x00000007ffc00000| Untracked 

Card table byte_map: [0x000001492fa50000,0x0000014930a50000] _byte_map_base: 0x000001492ca50000

Marking Bits: (CMBitMap*) 0x000001490aef8900
 Bits: [0x0000014930a50000, 0x0000014938a50000)

Polling page: 0x0000014908e60000

Metaspace:

Usage:
  Non-class:    140.07 MB used.
      Class:     22.52 MB used.
       Both:    162.59 MB used.

Virtual space:
  Non-class space:      192.00 MB reserved,     141.25 MB ( 74%) committed,  3 nodes.
      Class space:        1.00 GB reserved,      23.75 MB (  2%) committed,  1 nodes.
             Both:        1.19 GB reserved,     165.00 MB ( 14%) committed. 

Chunk freelists:
   Non-Class:  2.45 MB
       Class:  8.14 MB
        Both:  10.59 MB

MaxMetaspaceSize: 4.00 GB
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 274.69 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 4808.
num_arena_deaths: 130.
num_vsnodes_births: 4.
num_vsnodes_deaths: 0.
num_space_committed: 2639.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 195.
num_chunks_taken_from_freelist: 11969.
num_chunk_merges: 84.
num_chunk_splits: 7945.
num_chunks_enlarged: 5252.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=258432Kb used=20561Kb max_used=20561Kb free=237870Kb
 bounds [0x000001491e350000, 0x000001491f770000, 0x000001492dfb0000]
CodeHeap 'profiled nmethods': size=258368Kb used=48153Kb max_used=48153Kb free=210214Kb
 bounds [0x000001490dfb0000, 0x0000014910ec0000, 0x000001491dc00000]
CodeHeap 'non-nmethods': size=7488Kb used=4156Kb max_used=4865Kb free=3332Kb
 bounds [0x000001491dc00000, 0x000001491e0d0000, 0x000001491e350000]
 total_blobs=24023 nmethods=22921 adapters=1003
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 45.660 Thread 0x00000149502de790 nmethod 28284 0x0000014910e6d410 code [0x0000014910e6da00, 0x0000014910e71028]
Event: 45.662 Thread 0x00000149502de790 28286   !   3       java.io.FileInputStream::open (26 bytes)
Event: 45.662 Thread 0x00000149502de790 nmethod 28286 0x000001491057df10 code [0x000001491057e0e0, 0x000001491057e308]
Event: 45.663 Thread 0x00000149412dbab0 28287       2       org.gradle.internal.classpath.declarations.FileInterceptorsDeclaration::intercept_isDirectory (6 bytes)
Event: 45.663 Thread 0x00000149412dbab0 nmethod 28287 0x00000149106c0210 code [0x00000149106c03e0, 0x00000149106c05b0]
Event: 45.673 Thread 0x00000149502de790 28290       2       com.android.tools.r8.internal.U3::a (27 bytes)
Event: 45.674 Thread 0x00000149502de790 nmethod 28290 0x00000149106bfc90 code [0x00000149106bfe40, 0x00000149106c00b8]
Event: 45.674 Thread 0x00000149502de790 28291       2       com.android.tools.r8.internal.ep0$$Lambda/0x00000008014d3a10::<init> (10 bytes)
Event: 45.675 Thread 0x00000149502de790 nmethod 28291 0x000001491057db90 code [0x000001491057dd20, 0x000001491057de78]
Event: 45.676 Thread 0x00000149502de790 28292       2       org.gradle.internal.snapshot.impl.DirectorySnapshotter$PathVisitor::visitResolvedFile (44 bytes)
Event: 45.677 Thread 0x00000149502de790 nmethod 28292 0x00000149106bf610 code [0x00000149106bf800, 0x00000149106bfae8]
Event: 45.680 Thread 0x00000149502de790 28293       3       com.android.tools.r8.internal.y7::c (2193 bytes)
Event: 45.688 Thread 0x00000149412dbab0 28294   !   2       org.gradle.internal.hash.DefaultStreamHasher::doHash (84 bytes)
Event: 45.689 Thread 0x00000149412dbab0 nmethod 28294 0x00000149106bdc10 code [0x00000149106bdf20, 0x00000149106beba8]
Event: 45.696 Thread 0x00000149412dbab0 28295       3       java.util.zip.ZipFile$InflaterCleanupAction::run (12 bytes)
Event: 45.696 Thread 0x00000149412dbab0 nmethod 28295 0x00000149106bd810 code [0x00000149106bd9c0, 0x00000149106bdb18]
Event: 45.708 Thread 0x00000149412dbab0 28298       2       com.android.tools.r8.internal.ep0$$Lambda/0x00000008014d0490::forEach (11 bytes)
Event: 45.708 Thread 0x00000149412dbab0 nmethod 28298 0x0000014910927210 code [0x00000149109273c0, 0x00000149109274f8]
Event: 45.714 Thread 0x00000149400de160 nmethod 28285% 0x0000014910e72210 code [0x0000014910e74a60, 0x0000014910e91e98]
Event: 45.715 Thread 0x00000149502de790 nmethod 28293 0x0000014910e9cb10 code [0x0000014910e9e4a0, 0x0000014910eb0388]

GC Heap History (20 events):
Event: 32.648 GC heap after
{Heap after GC invocations=71 (full 0):
 garbage-first heap   total 618496K, used 300783K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 7 survivors (28672K)
 Metaspace       used 165406K, committed 167872K, reserved 1245184K
  class space    used 23002K, committed 24256K, reserved 1048576K
}
Event: 32.960 GC heap before
{Heap before GC invocations=71 (full 0):
 garbage-first heap   total 618496K, used 550639K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 68 young (278528K), 7 survivors (28672K)
 Metaspace       used 165478K, committed 168000K, reserved 1245184K
  class space    used 23015K, committed 24320K, reserved 1048576K
}
Event: 32.998 GC heap after
{Heap after GC invocations=72 (full 0):
 garbage-first heap   total 880640K, used 306717K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 6 survivors (24576K)
 Metaspace       used 165478K, committed 168000K, reserved 1245184K
  class space    used 23015K, committed 24320K, reserved 1048576K
}
Event: 33.973 GC heap before
{Heap before GC invocations=73 (full 0):
 garbage-first heap   total 880640K, used 798237K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 127 young (520192K), 6 survivors (24576K)
 Metaspace       used 165892K, committed 168384K, reserved 1245184K
  class space    used 23038K, committed 24320K, reserved 1048576K
}
Event: 33.988 GC heap after
{Heap after GC invocations=74 (full 0):
 garbage-first heap   total 880640K, used 320935K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 9 young (36864K), 9 survivors (36864K)
 Metaspace       used 165892K, committed 168384K, reserved 1245184K
  class space    used 23038K, committed 24320K, reserved 1048576K
}
Event: 34.736 GC heap before
{Heap before GC invocations=74 (full 0):
 garbage-first heap   total 880640K, used 787879K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 123 young (503808K), 9 survivors (36864K)
 Metaspace       used 166039K, committed 168512K, reserved 1245184K
  class space    used 23040K, committed 24320K, reserved 1048576K
}
Event: 34.755 GC heap after
{Heap after GC invocations=75 (full 0):
 garbage-first heap   total 880640K, used 323031K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 11 survivors (45056K)
 Metaspace       used 166039K, committed 168512K, reserved 1245184K
  class space    used 23040K, committed 24320K, reserved 1048576K
}
Event: 35.633 GC heap before
{Heap before GC invocations=75 (full 0):
 garbage-first heap   total 880640K, used 781783K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 123 young (503808K), 11 survivors (45056K)
 Metaspace       used 166071K, committed 168576K, reserved 1245184K
  class space    used 23040K, committed 24320K, reserved 1048576K
}
Event: 35.649 GC heap after
{Heap after GC invocations=76 (full 0):
 garbage-first heap   total 880640K, used 329583K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 6 survivors (24576K)
 Metaspace       used 166071K, committed 168576K, reserved 1245184K
  class space    used 23040K, committed 24320K, reserved 1048576K
}
Event: 36.837 GC heap before
{Heap before GC invocations=77 (full 0):
 garbage-first heap   total 880640K, used 804719K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 122 young (499712K), 6 survivors (24576K)
 Metaspace       used 166169K, committed 168640K, reserved 1245184K
  class space    used 23043K, committed 24320K, reserved 1048576K
}
Event: 36.849 GC heap after
{Heap after GC invocations=78 (full 0):
 garbage-first heap   total 880640K, used 331170K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 7 survivors (28672K)
 Metaspace       used 166169K, committed 168640K, reserved 1245184K
  class space    used 23043K, committed 24320K, reserved 1048576K
}
Event: 38.956 GC heap before
{Heap before GC invocations=78 (full 0):
 garbage-first heap   total 880640K, used 798114K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 121 young (495616K), 7 survivors (28672K)
 Metaspace       used 166244K, committed 168704K, reserved 1245184K
  class space    used 23045K, committed 24320K, reserved 1048576K
}
Event: 38.971 GC heap after
{Heap after GC invocations=79 (full 0):
 garbage-first heap   total 880640K, used 322357K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 7 survivors (28672K)
 Metaspace       used 166244K, committed 168704K, reserved 1245184K
  class space    used 23045K, committed 24320K, reserved 1048576K
}
Event: 42.616 GC heap before
{Heap before GC invocations=79 (full 0):
 garbage-first heap   total 880640K, used 797493K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 124 young (507904K), 7 survivors (28672K)
 Metaspace       used 166313K, committed 168768K, reserved 1245184K
  class space    used 23050K, committed 24320K, reserved 1048576K
}
Event: 42.627 GC heap after
{Heap after GC invocations=80 (full 0):
 garbage-first heap   total 880640K, used 327898K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 9 young (36864K), 9 survivors (36864K)
 Metaspace       used 166313K, committed 168768K, reserved 1245184K
  class space    used 23050K, committed 24320K, reserved 1048576K
}
Event: 43.942 GC heap before
{Heap before GC invocations=81 (full 0):
 garbage-first heap   total 880640K, used 790746K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 122 young (499712K), 9 survivors (36864K)
 Metaspace       used 166328K, committed 168768K, reserved 1245184K
  class space    used 23051K, committed 24320K, reserved 1048576K
}
Event: 43.963 GC heap after
{Heap after GC invocations=82 (full 0):
 garbage-first heap   total 880640K, used 347196K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 13 young (53248K), 13 survivors (53248K)
 Metaspace       used 166328K, committed 168768K, reserved 1245184K
  class space    used 23051K, committed 24320K, reserved 1048576K
}
Event: 44.862 GC heap before
{Heap before GC invocations=82 (full 0):
 garbage-first heap   total 880640K, used 777276K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 118 young (483328K), 13 survivors (53248K)
 Metaspace       used 166348K, committed 168832K, reserved 1245184K
  class space    used 23051K, committed 24320K, reserved 1048576K
}
Event: 44.882 GC heap after
{Heap after GC invocations=83 (full 0):
 garbage-first heap   total 880640K, used 312990K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 8 survivors (32768K)
 Metaspace       used 166348K, committed 168832K, reserved 1245184K
  class space    used 23051K, committed 24320K, reserved 1048576K
}
Event: 45.717 GC heap before
{Heap before GC invocations=83 (full 0):
 garbage-first heap   total 880640K, used 796318K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 126 young (516096K), 8 survivors (32768K)
 Metaspace       used 166494K, committed 168960K, reserved 1245184K
  class space    used 23057K, committed 24320K, reserved 1048576K
}

Dll operation events (3 events):
Event: 0.010 Loaded shared library C:\Program Files\Android\Android Studio1\jbr\bin\java.dll
Event: 0.015 Loaded shared library C:\Program Files\Android\Android Studio1\jbr\bin\zip.dll
Event: 0.528 Loaded shared library C:\Program Files\Android\Android Studio1\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 45.660 Thread 0x0000014945da2c90 DEOPT PACKING pc=0x0000014910681953 sp=0x00000002c5ef75b0
Event: 45.660 Thread 0x0000014945da2c90 DEOPT UNPACKING pc=0x000001491dc54e42 sp=0x00000002c5ef6e58 mode 0
Event: 45.665 Thread 0x000001494729c230 DEOPT PACKING pc=0x0000014910e59c85 sp=0x00000002c77fa610
Event: 45.665 Thread 0x000001494729c230 DEOPT UNPACKING pc=0x000001491dc54e42 sp=0x00000002c77f9cc8 mode 0
Event: 45.667 Thread 0x000001494729c230 DEOPT PACKING pc=0x0000014910e36dce sp=0x00000002c77fa490
Event: 45.667 Thread 0x000001494729c230 DEOPT UNPACKING pc=0x000001491dc54e42 sp=0x00000002c77f9d40 mode 0
Event: 45.674 Thread 0x000001494729c230 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001491f6812f8 relative=0x0000000000001318
Event: 45.674 Thread 0x000001494729c230 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001491f6812f8 method=com.android.tools.r8.internal.y7.c(Lcom/android/tools/r8/internal/aA;)Lcom/android/tools/r8/internal/t7; @ 1553 c2
Event: 45.674 Thread 0x000001494729c230 DEOPT PACKING pc=0x000001491f6812f8 sp=0x00000002c77fa5a0
Event: 45.674 Thread 0x000001494729c230 DEOPT UNPACKING pc=0x000001491dc546a2 sp=0x00000002c77fa5a8 mode 2
Event: 45.688 Thread 0x000001494729b510 DEOPT PACKING pc=0x000001490fec1e3a sp=0x00000002c75fb7b0
Event: 45.688 Thread 0x000001494729b510 DEOPT UNPACKING pc=0x000001491dc54e42 sp=0x00000002c75faca0 mode 0
Event: 45.693 Thread 0x0000014945da2c90 DEOPT PACKING pc=0x0000014910e373fa sp=0x00000002c5ef7b10
Event: 45.693 Thread 0x0000014945da2c90 DEOPT UNPACKING pc=0x000001491dc54e42 sp=0x00000002c5ef73c8 mode 0
Event: 45.695 Thread 0x000001494729bba0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001491f37355c relative=0x000000000000023c
Event: 45.695 Thread 0x000001494729bba0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001491f37355c method=java.util.Collections$SetFromMap.clear()V @ 4 c2
Event: 45.696 Thread 0x000001494729bba0 DEOPT PACKING pc=0x000001491f37355c sp=0x00000002c76fbf60
Event: 45.696 Thread 0x000001494729bba0 DEOPT UNPACKING pc=0x000001491dc546a2 sp=0x00000002c76fbf18 mode 2
Event: 45.717 Thread 0x000001494729c230 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001491f5352e8 relative=0x0000000000000b08
Event: 45.717 Thread 0x000001494729c230 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001491f5352e8 method=com.android.tools.r8.internal.QF.contains(Ljava/lang/Object;)Z @ 144 c2

Classes loaded (20 events):
Event: 29.630 Loading class java/util/AbstractList$SubList
Event: 29.630 Loading class java/util/AbstractList$SubList done
Event: 29.630 Loading class java/util/AbstractList$SubList$1
Event: 29.630 Loading class java/util/AbstractList$SubList$1 done
Event: 31.023 Loading class java/util/concurrent/ConcurrentHashMap$TreeNode
Event: 31.023 Loading class java/util/concurrent/ConcurrentHashMap$TreeNode done
Event: 31.023 Loading class java/util/concurrent/ConcurrentHashMap$TreeBin
Event: 31.024 Loading class java/util/concurrent/ConcurrentHashMap$TreeBin done
Event: 31.966 Loading class java/nio/ShortBuffer
Event: 31.967 Loading class java/nio/ShortBuffer done
Event: 32.066 Loading class java/nio/ByteBufferAsShortBufferL
Event: 32.067 Loading class java/nio/ByteBufferAsShortBufferL done
Event: 32.110 Loading class java/util/zip/Adler32
Event: 32.154 Loading class java/util/zip/Adler32 done
Event: 32.158 Loading class java/nio/file/Files$2
Event: 32.158 Loading class java/nio/file/Files$2 done
Event: 32.260 Loading class java/nio/file/StandardCopyOption
Event: 32.261 Loading class java/nio/file/StandardCopyOption done
Event: 32.261 Loading class sun/nio/fs/WindowsFileCopy
Event: 32.261 Loading class sun/nio/fs/WindowsFileCopy done

Classes unloaded (20 events):
Event: 29.951 Thread 0x000001493b154870 Unloading class 0x000000080143d400 'java/lang/invoke/LambdaForm$DMH+0x000000080143d400'
Event: 31.676 Thread 0x000001493b154870 Unloading class 0x00000008016a2c00 'java/lang/invoke/LambdaForm$DMH+0x00000008016a2c00'
Event: 31.676 Thread 0x000001493b154870 Unloading class 0x00000008016a2400 'java/lang/invoke/LambdaForm$DMH+0x00000008016a2400'
Event: 31.676 Thread 0x000001493b154870 Unloading class 0x00000008016a1800 'java/lang/invoke/LambdaForm$DMH+0x00000008016a1800'
Event: 31.676 Thread 0x000001493b154870 Unloading class 0x00000008016a1c00 'java/lang/invoke/LambdaForm$DMH+0x00000008016a1c00'
Event: 31.676 Thread 0x000001493b154870 Unloading class 0x00000008016a1400 'java/lang/invoke/LambdaForm$DMH+0x00000008016a1400'
Event: 31.676 Thread 0x000001493b154870 Unloading class 0x00000008016a0c00 'java/lang/invoke/LambdaForm$DMH+0x00000008016a0c00'
Event: 31.676 Thread 0x000001493b154870 Unloading class 0x00000008016a0800 'java/lang/invoke/LambdaForm$DMH+0x00000008016a0800'
Event: 31.676 Thread 0x000001493b154870 Unloading class 0x00000008016a2800 'java/lang/invoke/LambdaForm$DMH+0x00000008016a2800'
Event: 31.676 Thread 0x000001493b154870 Unloading class 0x00000008016a1000 'java/lang/invoke/LambdaForm$DMH+0x00000008016a1000'
Event: 31.676 Thread 0x000001493b154870 Unloading class 0x00000008016a2000 'java/lang/invoke/LambdaForm$DMH+0x00000008016a2000'
Event: 31.676 Thread 0x000001493b154870 Unloading class 0x00000008016a0000 'java/lang/invoke/LambdaForm$DMH+0x00000008016a0000'
Event: 31.676 Thread 0x000001493b154870 Unloading class 0x0000000801648400 'java/lang/invoke/LambdaForm$DMH+0x0000000801648400'
Event: 31.676 Thread 0x000001493b154870 Unloading class 0x0000000801648c00 'java/lang/invoke/LambdaForm$DMH+0x0000000801648c00'
Event: 31.676 Thread 0x000001493b154870 Unloading class 0x0000000801648800 'java/lang/invoke/LambdaForm$DMH+0x0000000801648800'
Event: 31.676 Thread 0x000001493b154870 Unloading class 0x0000000801648000 'java/lang/invoke/LambdaForm$DMH+0x0000000801648000'
Event: 33.245 Thread 0x000001493b154870 Unloading class 0x00000008016ec800 'java/lang/invoke/LambdaForm$MH+0x00000008016ec800'
Event: 33.245 Thread 0x000001493b154870 Unloading class 0x00000008016ec400 'java/lang/invoke/LambdaForm$DMH+0x00000008016ec400'
Event: 33.245 Thread 0x000001493b154870 Unloading class 0x000000080143e000 'java/lang/invoke/LambdaForm$DMH+0x000000080143e000'
Event: 33.245 Thread 0x000001493b154870 Unloading class 0x000000080158c800 'java/lang/invoke/LambdaForm$DMH+0x000000080158c800'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 38.915 Thread 0x000001494729c230 Exception <a 'sun/nio/fs/WindowsException'{0x000000061bd6d500}> (0x000000061bd6d500) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 40.565 Thread 0x000001494729ae80 Exception <a 'sun/nio/fs/WindowsException'{0x000000062da1e1b0}> (0x000000062da1e1b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 41.028 Thread 0x000001494729ae80 Exception <a 'sun/nio/fs/WindowsException'{0x000000062a54c240}> (0x000000062a54c240) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 41.359 Thread 0x000001494729ae80 Exception <a 'sun/nio/fs/WindowsException'{0x0000000628fd85e8}> (0x0000000628fd85e8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 41.673 Thread 0x000001494729ae80 Exception <a 'sun/nio/fs/WindowsException'{0x00000006264822e0}> (0x00000006264822e0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 41.922 Thread 0x000001494729ae80 Exception <a 'sun/nio/fs/WindowsException'{0x000000062310cd50}> (0x000000062310cd50) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 42.391 Thread 0x000001494729ae80 Exception <a 'sun/nio/fs/WindowsException'{0x000000061bcea728}> (0x000000061bcea728) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.409 Thread 0x0000014945da2c90 Exception <a 'sun/nio/fs/WindowsException'{0x000000062ac8f6b0}> (0x000000062ac8f6b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.554 Thread 0x00000149472a0a60 Exception <a 'sun/nio/fs/WindowsException'{0x000000062334d268}> (0x000000062334d268) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.560 Thread 0x00000149472a0a60 Implicit null exception at 0x000001491ec88f33 to 0x000001491ec8b85c
Event: 45.627 Thread 0x000001494729a160 Implicit null exception at 0x000001491f34a25e to 0x000001491f34a698
Event: 45.629 Thread 0x0000014945da3320 Exception <a 'sun/nio/fs/WindowsException'{0x00000006213f7890}> (0x00000006213f7890) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.632 Thread 0x0000014945da3320 Implicit null exception at 0x000001491ee662ca to 0x000001491ee66a8c
Event: 45.642 Thread 0x000001494729bba0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000620319560}> (0x0000000620319560) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.645 Thread 0x000001494729a160 Exception <a 'sun/nio/fs/WindowsException'{0x000000061d0a7ad8}> (0x000000061d0a7ad8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.646 Thread 0x0000014947ada280 Exception <a 'sun/nio/fs/WindowsException'{0x000000061cebfd10}> (0x000000061cebfd10) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.661 Thread 0x0000014947ad81b0 Exception <a 'sun/nio/fs/WindowsException'{0x000000061b616fd0}> (0x000000061b616fd0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.670 Thread 0x0000014947ad9bf0 Exception <a 'sun/nio/fs/WindowsException'{0x000000061bdd0158}> (0x000000061bdd0158) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.686 Thread 0x000001494729a160 Exception <a 'sun/nio/fs/WindowsException'{0x000000061d0cf8d0}> (0x000000061d0cf8d0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.713 Thread 0x000001494729a160 Exception <a 'sun/nio/fs/WindowsException'{0x0000000617577f50}> (0x0000000617577f50) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 42.322 Executing VM operation: ICBufferFull done
Event: 42.615 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 42.628 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 42.789 Executing VM operation: G1PauseRemark
Event: 42.800 Executing VM operation: G1PauseRemark done
Event: 42.883 Executing VM operation: G1PauseCleanup
Event: 42.884 Executing VM operation: G1PauseCleanup done
Event: 43.604 Executing VM operation: ICBufferFull
Event: 43.604 Executing VM operation: ICBufferFull done
Event: 43.942 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 43.963 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 44.742 Executing VM operation: ICBufferFull
Event: 44.742 Executing VM operation: ICBufferFull done
Event: 44.862 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 44.882 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 45.376 Executing VM operation: ICBufferFull
Event: 45.377 Executing VM operation: ICBufferFull done
Event: 45.632 Executing VM operation: ICBufferFull
Event: 45.632 Executing VM operation: ICBufferFull done
Event: 45.714 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 42.796 Thread 0x000001493b154870 flushing  nmethod 0x00000149105de090
Event: 42.796 Thread 0x000001493b154870 flushing  nmethod 0x00000149105de490
Event: 42.796 Thread 0x000001493b154870 flushing  nmethod 0x00000149105dea90
Event: 42.796 Thread 0x000001493b154870 flushing  nmethod 0x00000149105df210
Event: 42.796 Thread 0x000001493b154870 flushing  nmethod 0x00000149105e0190
Event: 42.796 Thread 0x000001493b154870 flushing  nmethod 0x00000149105e0e90
Event: 42.796 Thread 0x000001493b154870 flushing  nmethod 0x00000149106bb110
Event: 42.796 Thread 0x000001493b154870 flushing  nmethod 0x00000149106bd410
Event: 42.796 Thread 0x000001493b154870 flushing  nmethod 0x00000149106bdc90
Event: 42.796 Thread 0x000001493b154870 flushing  nmethod 0x00000149106bec90
Event: 42.796 Thread 0x000001493b154870 flushing  nmethod 0x00000149106c0290
Event: 42.796 Thread 0x000001493b154870 flushing  nmethod 0x00000149108dd610
Event: 42.796 Thread 0x000001493b154870 flushing  nmethod 0x00000149108e1e90
Event: 42.796 Thread 0x000001493b154870 flushing  nmethod 0x00000149108e4910
Event: 42.796 Thread 0x000001493b154870 flushing  nmethod 0x00000149108e6410
Event: 42.796 Thread 0x000001493b154870 flushing  nmethod 0x00000149108e8510
Event: 42.796 Thread 0x000001493b154870 flushing  nmethod 0x00000149108ecb90
Event: 42.796 Thread 0x000001493b154870 flushing  nmethod 0x00000149108ee590
Event: 42.796 Thread 0x000001493b154870 flushing  nmethod 0x00000149109e9a90
Event: 42.796 Thread 0x000001493b154870 flushing  nmethod 0x00000149109eb110

Events (20 events):
Event: 27.854 Loaded shared library C:\Program Files\Android\Android Studio1\jbr\bin\rmi.dll
Event: 27.856 Thread 0x00000149456fab20 Thread added: 0x00000149456fb1b0
Event: 27.873 Thread 0x00000149415e1850 Thread exited: 0x00000149415e1850
Event: 27.985 Thread 0x00000149456fab20 Thread added: 0x00000149456f83c0
Event: 27.985 Thread 0x00000149456fab20 Thread added: 0x00000149456fd280
Event: 28.050 Thread 0x00000149456f83c0 Thread added: 0x00000149456fb840
Event: 28.092 Thread 0x00000149415e4130 Thread exited: 0x00000149415e4130
Event: 28.114 Thread 0x00000149458019f0 Thread exited: 0x00000149458019f0
Event: 28.340 Thread 0x00000149402abd10 Thread added: 0x00000149412d9f70
Event: 28.362 Thread 0x00000149402abd10 Thread added: 0x00000149412da640
Event: 28.364 Thread 0x00000149400de160 Thread added: 0x00000149412d91d0
Event: 28.378 Thread 0x00000149400de160 Thread added: 0x00000149412dbab0
Event: 28.423 Thread 0x00000149400dc6c0 Thread added: 0x00000149412dc180
Event: 28.686 Thread 0x00000149412dc180 Thread exited: 0x00000149412dc180
Event: 38.316 Thread 0x00000149402abd10 Thread exited: 0x00000149402abd10
Event: 40.495 Thread 0x0000014940299680 Thread exited: 0x0000014940299680
Event: 40.495 Thread 0x000001494027e500 Thread exited: 0x000001494027e500
Event: 40.495 Thread 0x00000149412dbab0 Thread exited: 0x00000149412dbab0
Event: 40.847 Thread 0x00000149400de160 Thread added: 0x00000149412dbab0
Event: 45.575 Thread 0x00000149400de160 Thread added: 0x00000149502de790


Dynamic libraries:
0x00007ff6733b0000 - 0x00007ff6733ba000 	C:\Program Files\Android\Android Studio1\jbr\bin\java.exe
0x00007ffd9cb60000 - 0x00007ffd9cd69000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffd9aaa0000 - 0x00007ffd9ab5d000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffd9a1e0000 - 0x00007ffd9a564000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffd9a750000 - 0x00007ffd9a861000 	C:\Windows\System32\ucrtbase.dll
0x00007ffd8eb60000 - 0x00007ffd8eb7b000 	C:\Program Files\Android\Android Studio1\jbr\bin\VCRUNTIME140.dll
0x00007ffd94050000 - 0x00007ffd94068000 	C:\Program Files\Android\Android Studio1\jbr\bin\jli.dll
0x00007ffd9c020000 - 0x00007ffd9c1cd000 	C:\Windows\System32\USER32.dll
0x00007ffd9a980000 - 0x00007ffd9a9a6000 	C:\Windows\System32\win32u.dll
0x00007ffd7d180000 - 0x00007ffd7d425000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22000.120_none_9d947278b86cc467\COMCTL32.dll
0x00007ffd9c650000 - 0x00007ffd9c67a000 	C:\Windows\System32\GDI32.dll
0x00007ffd9b120000 - 0x00007ffd9b1c3000 	C:\Windows\System32\msvcrt.dll
0x00007ffd9a630000 - 0x00007ffd9a74e000 	C:\Windows\System32\gdi32full.dll
0x00007ffd9a870000 - 0x00007ffd9a90d000 	C:\Windows\System32\msvcp_win.dll
0x00007ffd9ac60000 - 0x00007ffd9ac91000 	C:\Windows\System32\IMM32.DLL
0x00007ffd94900000 - 0x00007ffd9490c000 	C:\Program Files\Android\Android Studio1\jbr\bin\vcruntime140_1.dll
0x00007ffd5a6a0000 - 0x00007ffd5a72d000 	C:\Program Files\Android\Android Studio1\jbr\bin\msvcp140.dll
0x00007ffd10fb0000 - 0x00007ffd11c3b000 	C:\Program Files\Android\Android Studio1\jbr\bin\server\jvm.dll
0x00007ffd9c7e0000 - 0x00007ffd9c88e000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffd9c680000 - 0x00007ffd9c71e000 	C:\Windows\System32\sechost.dll
0x00007ffd9b4e0000 - 0x00007ffd9b601000 	C:\Windows\System32\RPCRT4.dll
0x00007ffd9ab60000 - 0x00007ffd9abcf000 	C:\Windows\System32\WS2_32.dll
0x00007ffd99ec0000 - 0x00007ffd99f0d000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffd92000000 - 0x00007ffd9200a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffd94790000 - 0x00007ffd947c3000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffd99ea0000 - 0x00007ffd99eb3000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffd99060000 - 0x00007ffd99078000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffd92ba0000 - 0x00007ffd92baa000 	C:\Program Files\Android\Android Studio1\jbr\bin\jimage.dll
0x00007ffd97af0000 - 0x00007ffd97d11000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffd79ae0000 - 0x00007ffd79b11000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffd9a160000 - 0x00007ffd9a1df000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffd7dcc0000 - 0x00007ffd7dcce000 	C:\Program Files\Android\Android Studio1\jbr\bin\instrument.dll
0x00007ffd7eac0000 - 0x00007ffd7eae0000 	C:\Program Files\Android\Android Studio1\jbr\bin\java.dll
0x00007ffd7e7d0000 - 0x00007ffd7e7e8000 	C:\Program Files\Android\Android Studio1\jbr\bin\zip.dll
0x00007ffd9b610000 - 0x00007ffd9bdd5000 	C:\Windows\System32\SHELL32.dll
0x00007ffd980c0000 - 0x00007ffd98922000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffd9ada0000 - 0x00007ffd9b116000 	C:\Windows\System32\combase.dll
0x00007ffd97f40000 - 0x00007ffd980a7000 	C:\Windows\SYSTEM32\wintypes.dll
0x00007ffd9aca0000 - 0x00007ffd9ad8a000 	C:\Windows\System32\SHCORE.dll
0x00007ffd9bfc0000 - 0x00007ffd9c01d000 	C:\Windows\System32\shlwapi.dll
0x00007ffd99f20000 - 0x00007ffd99f45000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffd8e920000 - 0x00007ffd8e930000 	C:\Program Files\Android\Android Studio1\jbr\bin\net.dll
0x00007ffd94e40000 - 0x00007ffd94f54000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffd99510000 - 0x00007ffd99577000 	C:\Windows\system32\mswsock.dll
0x00007ffd7dcf0000 - 0x00007ffd7dd06000 	C:\Program Files\Android\Android Studio1\jbr\bin\nio.dll
0x00007ffd8a050000 - 0x00007ffd8a060000 	C:\Program Files\Android\Android Studio1\jbr\bin\verify.dll
0x00007ffd7ce10000 - 0x00007ffd7ce37000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x000000005c620000 - 0x000000005c693000 	C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffd83450000 - 0x00007ffd83459000 	C:\Program Files\Android\Android Studio1\jbr\bin\management.dll
0x00007ffd7dce0000 - 0x00007ffd7dceb000 	C:\Program Files\Android\Android Studio1\jbr\bin\management_ext.dll
0x00007ffd9bde0000 - 0x00007ffd9bde8000 	C:\Windows\System32\PSAPI.DLL
0x00007ffd99750000 - 0x00007ffd99768000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffd98fc0000 - 0x00007ffd98ff5000 	C:\Windows\system32\rsaenh.dll
0x00007ffd99600000 - 0x00007ffd9962c000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffd998d0000 - 0x00007ffd998f7000 	C:\Windows\SYSTEM32\bcrypt.dll
0x00007ffd99770000 - 0x00007ffd9977c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffd98b80000 - 0x00007ffd98bad000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffd9bfb0000 - 0x00007ffd9bfb9000 	C:\Windows\System32\NSI.dll
0x00007ffd7dcd0000 - 0x00007ffd7dcd9000 	C:\Program Files\Android\Android Studio1\jbr\bin\extnet.dll
0x00007ffd7cd60000 - 0x00007ffd7cd68000 	C:\Windows\system32\wshunix.dll
0x000000005c5a0000 - 0x000000005c613000 	C:\Users\<USER>\AppData\Local\Temp\native-platform1084550657994001264dir\gradle-fileevents.dll
0x00007ffd72f30000 - 0x00007ffd72f47000 	C:\Windows\system32\napinsp.dll
0x00007ffd72f10000 - 0x00007ffd72f2b000 	C:\Windows\system32\pnrpnsp.dll
0x00007ffd98bb0000 - 0x00007ffd98c97000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffd72ef0000 - 0x00007ffd72f02000 	C:\Windows\System32\winrnr.dll
0x00007ffd72ed0000 - 0x00007ffd72ee5000 	C:\Windows\system32\wshbth.dll
0x00007ffd72eb0000 - 0x00007ffd72ecf000 	C:\Windows\system32\nlansp_c.dll
0x00007ffd93460000 - 0x00007ffd9346a000 	C:\Windows\System32\rasadhlp.dll
0x00007ffd93140000 - 0x00007ffd931c1000 	C:\Windows\System32\fwpuclnt.dll
0x00007ffd94ae0000 - 0x00007ffd94ae7000 	C:\Program Files\Android\Android Studio1\jbr\bin\rmi.dll
0x00007ffd99080000 - 0x00007ffd990b4000 	C:\Windows\SYSTEM32\ntmarta.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio1\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22000.120_none_9d947278b86cc467;C:\Program Files\Android\Android Studio1\jbr\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu;C:\Users\<USER>\AppData\Local\Temp\native-platform1084550657994001264dir

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError -Xmx8G -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\gradle-daemon-main-8.12.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 260046848                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8589934592                                {product} {command line}
   size_t MaxMetaspaceSize                         = 4294967296                                {product} {command line}
   size_t MaxNewSize                               = 5150605312                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 264634216                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 264634216                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 536870912                              {pd product} {command line}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8589934592                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Android\Android Studio1\jbr
CLASSPATH=C:\Users\<USER>\Desktop\SportsApp project\sportsapp\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Program Files\Android\Android Studio1\jbr\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\nodejs\;C:\Program Files\WindowsPowerShell\Scripts\HP.ClientScriptLibrary;C:\MinGW\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\flutter\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\HP\HP One Agent;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\flutter\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\flutter\bin\mingit\cmd;C:\Users\<USER>\flutter\bin\mingit\cmd
USERNAME=hp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 96 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 44, weak refs: 121

JNI global refs memory usage: 835, weak refs: 2729

Process memory usage:
Resident Set Size: 1448680K (9% of 16074040K total physical memory with 412368K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: 72290K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 47743K
Loader bootstrap                                                                       : 31401K
Loader org.gradle.initialization.MixInLegacyTypesClassLoader                           : 13514K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 521K
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 436K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 232K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 184K
Loader org.gradle.groovy.scripts.internal.DefaultScriptCompilationHandler$ScriptClassLoader: 147K
Loader org.codehaus.groovy.runtime.callsite.CallSiteClassLoader                        : 22104B

Classes loaded by more than one classloader:
Class Program                                                                         : loaded 11 times (x 68B)
Class Build_gradle$1                                                                  : loaded 4 times (x 71B)
Class Build_gradle                                                                    : loaded 4 times (x 126B)
Class Settings_gradle                                                                 : loaded 3 times (x 124B)
Class Build_gradle$2                                                                  : loaded 3 times (x 70B)
Class com.google.common.collect.RegularImmutableMap$KeySet                            : loaded 2 times (x 146B)
Class org.gradle.internal.classloader.FilteringClassLoader                            : loaded 2 times (x 101B)
Class com.google.common.io.FileWriteMode                                              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinVariantWithCoordinates             : loaded 2 times (x 104B)
Class org.jetbrains.kotlin.gradle.tasks.CInteropProcess                               : loaded 2 times (x 343B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDomApiDependencyManagementKt         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$launch$coroutine$1 : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt$defaultNpmDependencyDelegate$1: loaded 2 times (x 163B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.MissingNativeStdlibChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.KotilnNativeConfigureBinariesSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$DisambiguationRule        : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$Companion                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationCacheStartParameterAccessorKt: loaded 2 times (x 67B)
Class [Lcom.google.common.base.AbstractIterator$State;                                : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$IsEither                                     : loaded 2 times (x 107B)
Class org.gradle.internal.classloader.ClassLoaderHierarchy                            : loaded 2 times (x 66B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedCollection            : loaded 2 times (x 120B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifierKt    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.report.UsesBuildMetricsService                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationSourceSetsContainerFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.MutableObservableSet                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinHierarchyDsl                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt$isNativeSourceSet$2: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.report.BuildReportType                              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$AllowedListAnonymizer: loaded 2 times (x 75B)
Class com.google.common.collect.ImmutableEnumSet                                      : loaded 2 times (x 142B)
Class com.google.common.util.concurrent.ExecutionError                                : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableList$1                                       : loaded 2 times (x 93B)
Class org.gradle.api.internal.classpath.ManifestUtil                                  : loaded 2 times (x 67B)
Class com.google.common.io.ByteArrayDataOutput                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$androidLayoutResources$1        : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.report.GradleBuildMetricsReporter                   : loaded 2 times (x 94B)
Class org.jetbrains.kotlin.gradle.utils.FutureKt$future$1                             : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinMetadataCompilation                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope                : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationFriendPathsResolver: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$ANDROID_ARM64                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinNativeHostSpecificMetadataArtifactKt$KotlinNativeHostSpecificMetadataArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinLegacyMetadataArtifactKt$KotlinLegacyMetadataArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSideEffect$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinMetadataDisambiguation: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$IntGradleProperty: loaded 2 times (x 72B)
Class com.google.common.collect.RegularImmutableMap                                   : loaded 2 times (x 117B)
Class com.google.common.collect.ImmutableMap$MapViewOfValuesAsSingletonSets           : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$compilerRunner$1        : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.CacheableTasksKt$cacheOnlyIfEnabledForKotlin$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform: loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PreConfigure: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupActionKt$KotlinProjectSetupCoroutine$1$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_ARM32                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinJsTargetDsl                    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImplKt                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DeprecatedKotlinNativeTargetsChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$WhenMappings       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerToolOptionsHelper           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetComponent                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradlePluginExtensionPointInternal     : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$OVERRIDE_VERSION_IF_NOT_SET: loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsBeanService$Companion$initStatsService$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion$getProvider$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$BooleanGradleProperty: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtension                          : loaded 2 times (x 118B)
Class [Lorg.objectweb.asm.Symbol;                                                     : loaded 2 times (x 65B)
Class org.objectweb.asm.ClassVisitor                                                  : loaded 2 times (x 84B)
Class com.google.common.collect.UnmodifiableListIterator                              : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$ProjectConfigurationResult$Failure: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet$MetadataDependencyTransformation: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$Companion          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain                    : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Parameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices                    : loaded 2 times (x 76B)
Class [Lorg.jetbrains.kotlin.statistics.ValueAnonymizer;                              : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$MemoizedCallable$value$2: loaded 2 times (x 75B)
Class org.objectweb.asm.MethodTooLargeException                                       : loaded 2 times (x 79B)
Class [Lcom.google.common.cache.CacheBuilder$OneWeigher;                              : loaded 2 times (x 65B)
Class com.google.common.base.Suppliers                                                : loaded 2 times (x 67B)
Class com.google.common.base.Platform$JdkPatternCompiler                              : loaded 2 times (x 71B)
Class org.gradle.internal.classloader.DefaultClassLoaderFactory                       : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$kotlinCompilerArgumentsLogLevel$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetWithTests                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnosticRenderingOptions: loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.KotlinVersion;                                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.UsesKotlinToolingDiagnosticsParameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsEnvironmentChecker    : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt$special$$inlined$extrasStoredFuture$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.report.BuildMetricsService                          : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy               : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy               : loaded 2 times (x 74B)
Class com.google.common.base.CharMatcher$None                                         : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.ClassLoaderVisitor                              : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetrics               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationLanguageSettingsConfigurator: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleKt$await$1              : loaded 2 times (x 84B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinCompilation;                         : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetComponentWithPublication     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateNativeCompileTasksSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithJsPresetFunctions      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupActionKt                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.report.ReportingSettings$Companion                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.report.ConfigureReporingKt                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.TypeUtils                                     : loaded 2 times (x 67B)
Class [Lcom.google.common.collect.ImmutableMapEntry;                                  : loaded 2 times (x 65B)
Class org.gradle.api.internal.classpath.EffectiveClassPath                            : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.utils.CompatibilityKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationConfigurationsContainer: loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableRangeSet                                     : loaded 2 times (x 111B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$ANDROID_ARM32                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.tasks.TasksProviderKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnostic                : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetFactory        : loaded 2 times (x 76B)
Class com.google.common.collect.ImmutableEnumMap                                      : loaded 2 times (x 121B)
Class com.google.common.collect.ListMultimap                                          : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableSet$SerializedForm                           : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.incremental.ClasspathChanges                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.buildtools.api.jvm.ClassSnapshotGranularity                : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.ExternalKotlinTargetApi                             : loaded 2 times (x 66B)
Class com.google.common.collect.Iterators$EmptyModifiableIterator                     : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.konan.target.KonanTarget                                   : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidPluginWithoutAndroidTargetChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidSourceSetLayoutV1SourceSetsNotFoundChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectCheckersRunnerKt$launchKotlinGradleProjectCheckers$1$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.targets.CreateDefaultTestRunSideEffectKt            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt$mapOrNull$1                : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy              : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy$OVERRIDE          : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$kotlinExperimentalTryNext$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactoriesConfigurator$Companion$getProvider$1: loaded 2 times (x 70B)
Class com.google.common.collect.Sets$1                                                : loaded 2 times (x 135B)
Class org.objectweb.asm.AnnotationVisitor                                             : loaded 2 times (x 74B)
Class com.google.common.base.Splitter                                                 : loaded 2 times (x 68B)
Class org.gradle.api.internal.DefaultClassPathProvider                                : loaded 2 times (x 72B)
Class com.google.common.collect.Maps$ViewCachingAbstractMap                           : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$3$classpathEntrySnapshotFiles$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.KaptGenerateStubsConfig$Companion$wireJavaAndKotlinOutputs$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleCoroutineContextElement : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker             : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KotlinSourceSetTreeDependsOnMismatchChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.CreateDefaultCompilationsSideEffectKt       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.SyncLanguageSettingsWithKotlinExtensionSetupActionKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.NumericalMetrics                        : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion$getProvider$2$1: loaded 2 times (x 75B)
Class com.google.common.collect.Sets$2                                                : loaded 2 times (x 135B)
Class [Lorg.gradle.api.internal.ClassPathProvider;                                    : loaded 2 times (x 65B)
Class org.gradle.api.internal.ClassPathProvider                                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.ExecutedTaskMetrics               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrors$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJvmCompile$DefaultImpls                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.CacheableTasksKt                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.HostManager$targetValues$2                    : loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.konan.target.Family;                                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.UnusedSourceSetsChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.PeerNpmDependencyExtension           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.AndroidMainSourceSetConventionsChecker          : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt$AddNpmDependencyExtensionProjectSetupAction$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildPerformanceMetric          : loaded 2 times (x 88B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$NullableBooleanGradleProperty: loaded 2 times (x 72B)
Class [Lorg.objectweb.asm.AnnotationVisitor;                                          : loaded 2 times (x 65B)
Class com.google.common.collect.Sets$3                                                : loaded 2 times (x 135B)
Class com.google.common.util.concurrent.AbstractFuture$SafeAtomicHelper               : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$LocalLoadingCache                            : loaded 2 times (x 130B)
Class com.google.common.base.AbstractIterator$State                                   : loaded 2 times (x 75B)
Class org.gradle.internal.service.UnknownServiceException                             : loaded 2 times (x 79B)
Class com.google.common.io.ByteArrayDataInput                                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2$1$1$1 : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ScriptFilterSpec                : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.ComposePluginSuggestApplyChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$TVOS_ARM64                        : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultPeerNpmDependencyExtension    : loaded 2 times (x 137B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleKt                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget                     : loaded 2 times (x 152B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$kotlinNativeVersion$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactoriesConfigurator$Inject: loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinSingleTargetExtension                     : loaded 2 times (x 120B)
Class com.google.common.collect.Sets$4                                                : loaded 2 times (x 135B)
Class org.objectweb.asm.ModuleVisitor                                                 : loaded 2 times (x 77B)
Class com.google.common.cache.LocalCache$Strength$1                                   : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2$1$1$2 : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$addSourcesToKotlinCompileTask$configureAction$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationTaskNamesContainer: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilerOptionsFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildServiceKt$propertyWithDeprecatedName$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtension               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.LanguageSettingsBuilder                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmWasiEnvironmentChecker: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker$3: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.ConfigureBuildSideEffectKt                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildEventsListenerRegistryHolder$Companion  : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.StringMetrics;                        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Inject   : loaded 2 times (x 92B)
Class com.google.common.util.concurrent.SettableFuture                                : loaded 2 times (x 110B)
Class com.google.common.cache.LocalCache$Strength$2                                   : loaded 2 times (x 77B)
Class com.google.common.io.ByteStreams                                                : loaded 2 times (x 67B)
Class com.google.common.collect.TransformedIterator                                   : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain$JdkSetter                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationCacheKt                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$ProjectConfigurationResult$Success: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$allKonanMainSubdirectoriesExist$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$CheckedPlatformInfo: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker$4: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinCinteropDisambiguation: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginInMultipleProjectsHolder         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.report.BuildReportType$Companion                    : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$Strength$3                                   : loaded 2 times (x 77B)
Class com.google.common.collect.MapDifference                                         : loaded 2 times (x 66B)
Class com.google.common.base.Splitter$1                                               : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerPluginData$InputsOutputsState   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$optInAnnotationsCheck$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinCompilationToRunnableFiles : loaded 2 times (x 192B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinMetadataArtifactKt$KotlinMetadataArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinMetadataCompatibility : loaded 2 times (x 72B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.JvmTarget;                                    : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt$maybeCreateResolvable$1      : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager$Companion         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.StringMetrics$Companion                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$property$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.tooling.core.Extras$Type                                   : loaded 2 times (x 68B)
Class org.objectweb.asm.FieldWriter                                                   : loaded 2 times (x 74B)
Class com.google.common.cache.CacheBuilder$NullListener                               : loaded 2 times (x 78B)
Class org.gradle.internal.classpath.TransformedClassPath                              : loaded 2 times (x 92B)
Class org.gradle.internal.classloader.InstrumentingClassLoader                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService$Companion       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService                 : loaded 2 times (x 76B)
Class com.google.common.collect.ImmutableRangeSet$AsSet                               : loaded 2 times (x 295B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$optInAnnotationsCheck$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.NativeForwardImplementationToApiElementsSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class com.google.common.cache.CacheLoader$FunctionToCacheLoader                       : loaded 2 times (x 71B)
Class com.google.common.collect.Iterators$ArrayItr                                    : loaded 2 times (x 93B)
Class com.google.common.collect.ImmutableCollection$Builder                           : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$3$2   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompileTool$sources$1           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.TaskConfigAction                : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$allKonanMainSubdirectoriesExist$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinPlugin$Companion               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSetTree                          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$Stage                  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateResourcesTaskSideEffectKt$KotlinCreateResourcesTaskSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinSourceSetFactory               : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Parameters_Decorated: loaded 2 times (x 139B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Companion  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager$subscribeForBuildResult$1$1: loaded 2 times (x 70B)
Class com.google.common.base.Joiner                                                   : loaded 2 times (x 75B)
Class com.google.common.base.MoreObjects                                              : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildAttribute;                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt$mapOrNull$1$1              : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifier$Default: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilationKt              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$attributes$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildTime                       : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.utils.IsolatedKotlinClasspathClassCastException     : loaded 2 times (x 78B)
Class [Lorg.objectweb.asm.Type;                                                       : loaded 2 times (x 65B)
Class com.google.common.collect.Platform                                              : loaded 2 times (x 67B)
Class com.google.common.collect.UnmodifiableIterator                                  : loaded 2 times (x 76B)
Class org.gradle.internal.service.ServiceLocator                                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJavaToolchainSetter$useAsConvention$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilerArgumentsProducer$CreateCompilerArgumentsContext: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.GradleAttributesContainerUtilsKt$copyAttributeTo$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.RenderReportedDiagnosticsKt      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifierKt$classify$1: loaded 2 times (x 84B)
Class com.google.common.collect.SingletonImmutableBiMap                               : loaded 2 times (x 139B)
Class [Lorg.jetbrains.kotlin.konan.target.KonanTarget;                                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmSourceSetsNotFoundChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidSourceSetLayoutV1SourceSetsNotFoundChecker$androidSourceSetRegex$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsLoggerService$Companion: loaded 2 times (x 67B)
Class com.google.common.base.Ascii                                                    : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$1                                            : loaded 2 times (x 85B)
Class com.google.common.collect.AbstractMapEntry                                      : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationWithResources               : loaded 2 times (x 66B)
Class com.google.common.collect.Sets$ImprovedAbstractSet                              : loaded 2 times (x 131B)
Class com.google.common.collect.ImmutableList$Builder                                 : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetPreset                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KotlinTargetAlreadyDeclaredChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationProcessorSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinJavaRuntimeJarsCompatibility: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.utils.FileUtilsKt$localProperties$1                 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.HasConfigurableKotlinCompilerOptions            : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$2                                            : loaded 2 times (x 138B)
Class com.google.common.collect.AbstractMultimap                                      : loaded 2 times (x 119B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$1 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain_Decorated          : loaded 2 times (x 122B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinProjectConfigurationMetrics : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfo$TCS                    : loaded 2 times (x 85B)
Class com.google.common.collect.ImmutableRangeSet$1                                   : loaded 2 times (x 205B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.HasBinaries                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonOptions                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateSourcesJarTaskSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmProjectExtension                       : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradlePluginExtensionPointInternalKt   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.UsesKotlinNativeBundleBuildService: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilerArgumentsProducer              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.ValueType                             : loaded 2 times (x 75B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper$1           : loaded 2 times (x 72B)
Class com.google.common.collect.RegularImmutableAsList                                : loaded 2 times (x 212B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTimes$Companion                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$classpathConfiguration$1: loaded 2 times (x 71B)
Class com.google.common.collect.Range                                                 : loaded 2 times (x 83B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionDelegate       : loaded 2 times (x 163B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecker           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport_Decorated               : loaded 2 times (x 120B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$jvmArgs$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.report.BuildMetricsService$Companion                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompileTool                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.SingleAction$PerformedActions                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$PropertyNames             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.GradleUtilsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.HasProject                                   : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Trusted                        : loaded 2 times (x 66B)
Class com.google.common.collect.Iterators                                             : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$RangesMatcher                                : loaded 2 times (x 108B)
Class com.google.common.io.ByteSink$AsCharSink                                        : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$3 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationTaskNamesContainerFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$CustomizeKotlinDependenciesSetupAction$1$coreLibrariesVersion$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.CInteropInputChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.ExperimentalKotlinGradlePluginApi                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader          : loaded 2 times (x 82B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildTime;                          : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy$SAFE          : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService                   : loaded 2 times (x 80B)
Class com.google.common.util.concurrent.UncheckedExecutionException                   : loaded 2 times (x 78B)
Class com.google.common.collect.Lists$StringAsImmutableList                           : loaded 2 times (x 203B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$4 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$scriptSources$1                 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttribute$Companion              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.CInteropInputChecker$runChecks$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.KaptGenerateStubsConfig$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt$maybeAddTestDependencyCapability$1: loaded 2 times (x 71B)
Class com.google.common.collect.ImmutableSortedSetFauxverideShim                      : loaded 2 times (x 143B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt$AddNpmDependencyExtensionProjectSetupAction$1$WhenMappings: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinJvmAndroidCompilation              : loaded 2 times (x 194B)
Class org.jetbrains.kotlin.gradle.targets.CreateDefaultCompilationsSideEffectKt$CreateDefaultCompilationsSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.AddKotlinPlatformIntegersSupportLibraryKt$AddKotlinPlatformIntegersSupportLibrary$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.ApplyUserDefinedAttributesKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.TypeUtils$WhenMappings                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.FutureImpl                                    : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$isToolchainEnabled$1: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.NumericalMetrics;                     : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$JavaLetterOrDigit                            : loaded 2 times (x 107B)
Class org.gradle.internal.InternalTransformer                                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.CompileUsingKotlinDaemonWithNormalization     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PostConfigure$Companion: loaded 2 times (x 67B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet$1                     : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDomApiDependencyManagementKt$configureKotlinDomApiDefaultDependency$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultDevNpmDependencyExtension     : loaded 2 times (x 141B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.HasKotlinDependencies;                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$Companion$wireJvmTargetToToolchain$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsLikeEnvironmentChecker: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType                           : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildServiceKt        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Companion          : loaded 2 times (x 67B)
Class org.objectweb.asm.ClassWriter                                                   : loaded 2 times (x 102B)
Class com.google.common.cache.CacheLoader$UnsupportedLoadingOperationException        : loaded 2 times (x 78B)
Class com.google.common.base.CharMatcher$And                                          : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.ClasspathUtil$1                                 : loaded 2 times (x 72B)
Class com.google.common.base.Objects                                                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.TransformActionUsingKotlinToolingDiagnostics$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService$Companion$registerIfAbsent$1: loaded 2 times (x 70B)
Class com.google.common.collect.AbstractMapBasedMultimap$Itr                          : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnosticRenderingOptions$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptionsHelper                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$Companion$wireJvmTargetToToolchain$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$ProjectLocalDisambiguation: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.apple.swiftexport.internal.SwiftExportInitKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$NullableStringGradleProperty: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$detectKotlinPluginLoadedInMultipleProjects$onRegister$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationCacheStartParameterAccessor$Factory: loaded 2 times (x 66B)
Class org.objectweb.asm.FieldVisitor                                                  : loaded 2 times (x 73B)
Class com.google.common.collect.IndexedImmutableSet                                   : loaded 2 times (x 146B)
Class com.google.common.collect.ImmutableMap$1                                        : loaded 2 times (x 77B)
Class com.google.common.collect.BiMap                                                 : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry$DefaultModule           : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.buildtools.api.KotlinLogger                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform$Parameters$Inject: loaded 2 times (x 114B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrors_Decorated: loaded 2 times (x 361B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$DefaultAddSourcesToCompileTask$addSources$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmSourceSetsNotFoundChecker$wasmSourceSetRegex$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSideEffect: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradlePluginExtensionPoint             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.tooling.core.ExtrasUtilsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.buildtools.api.ExperimentalBuildToolsApi                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ResourceUtilsKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt$kotlinPluginVersionFromResources$1: loaded 2 times (x 74B)
Class com.google.common.primitives.Ints                                               : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractIterator                                      : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.tasks.UsesKotlinJavaToolchain$DefaultImpls          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributeKind$Companion          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImplKt$KotlinTargetSoftwareComponent$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.KaptGenerateStubsConfig$Companion$configureLibraries$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$DefaultAddSourcesToCompileTask$addSources$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile                         : loaded 2 times (x 449B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationOutput                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt$addDependsOnFromTasksThatShouldFailWhenErrorsReported$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$TVOS_X64                          : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters$Inject  : loaded 2 times (x 102B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion$registerIfAbsentImpl$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt$kotlinPluginVersionFromResources$1$1: loaded 2 times (x 67B)
Class com.google.common.math.MathPreconditions                                        : loaded 2 times (x 67B)
Class com.google.common.collect.Multiset                                              : loaded 2 times (x 66B)
Class com.google.common.collect.ArrayListMultimapGwtSerializationDependencies         : loaded 2 times (x 168B)
Class com.google.common.collect.ObjectArrays                                          : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractIndexedListIterator                           : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.tooling.core.Extras$Entry                                  : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.InternalKotlinSourceSetKt            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin$Kapt3SubpluginContext : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinTargetArtifact                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptionsDefault_Decorated       : loaded 2 times (x 164B)
Class org.jetbrains.kotlin.gradle.plugin.HasKotlinDependencies                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildEventsListenerRegistryHolder_Decorated  : loaded 2 times (x 115B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion$registerIfAbsentImpl$2$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.MetricContainer                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.old.Pre232IdeaKotlinBuildStatsMXBean: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsServicesRegistry  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactoriesConfigurator   : loaded 2 times (x 72B)
Class com.google.common.collect.ImmutableMapEntrySet                                  : loaded 2 times (x 147B)
Class org.gradle.api.internal.classpath.Module                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$getToolsJarFromJvm$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ClasspathSnapshotProperties_Decorated: loaded 2 times (x 133B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform$Parameters_Decorated: loaded 2 times (x 151B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$Companion$includedSourceSets$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt$KotlinToolingDiagnosticsSetupAction$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDirectoryDependencyWithExternalsExtension: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupAction                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleFinishBuildHandler$Companion     : loaded 2 times (x 67B)
Class com.google.common.cache.Weigher                                                 : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.UnknownModuleException                        : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetComponentKt          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTarget$DefaultImpls        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$executeCurrentStageAndScheduleNext$3: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationRegisterInSourceSetsConfigurator$configure$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt$KotlinToolingDiagnosticsSetupAction$1$2: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyWithExternalsExtension  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsageContext                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetsContainer                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.compilerRunner.GradleCliCommonizerKt                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Parameters        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.PluginWrappersKt                             : loaded 2 times (x 67B)
Class com.google.common.base.Stopwatch                                                : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$Strength                                     : loaded 2 times (x 77B)
Class com.google.common.cache.Cache                                                   : loaded 2 times (x 66B)
Class com.google.common.io.Files$2                                                    : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.build.report.metrics.BuildMetricsReporter                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetKt                   : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractMultimap$Entries                              : loaded 2 times (x 116B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt$KotlinToolingDiagnosticsSetupAction$1$3: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinTargetWithNodeJsDsl            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt$defaultSourceSetLanguageSettingsChecker$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.sources.InternalKotlinSourceSet              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJvmPlugin                              : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.SyncLanguageSettingsWithKotlinExtensionSetupActionKt$SyncLanguageSettingsWithKotlinExtensionSetupAction$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinGradlePluginPublicDsl                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativeProperties                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.BuildSessionLogger                              : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationCacheStartParameterAccessor: loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Cancellation                   : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMap$Builder                                  : loaded 2 times (x 78B)
Class com.google.common.base.NullnessCasts                                            : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedCollection$WrappedIterator: loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.utils.FutureKt$future$2$1                           : loaded 2 times (x 91B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.statistics.plugins.ObservablePlugins;      : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.internal.BuildIdService$Companion$registerIfAbsent$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationDependencyConfigurationsFactoriesKt$WhenMappings: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$IOS_ARM64                         : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmTargetDsl                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DisabledCinteropCommonizationInHmppProjectChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.sources.ConsistencyCheck                     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt$defaultSourceSetLanguageSettingsChecker$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonToolOptions                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinPlugin                         : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$Companion$wireJvmTargetToJvm$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.ComposePluginSuggestApplyChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinMultiplatformExtension                    : loaded 2 times (x 697B)
Class com.google.common.cache.RemovalListener                                         : loaded 2 times (x 66B)
Class org.gradle.api.internal.DefaultClassPathRegistry                                : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImplKt$KotlinTargetSoftwareComponent$1$1$configuration$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultPeerNpmDependencyExtension$delegate$1: loaded 2 times (x 163B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency                        : loaded 2 times (x 93B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultNpmDependencyExtension        : loaded 2 times (x 148B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency$Scope                  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$unstableFeaturesCheck$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet               : loaded 2 times (x 137B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidSourceSetLayoutV1SourceSetsNotFoundChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt$sam$org_gradle_api_Action$0  : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Parameters    : loaded 2 times (x 66B)
Class com.google.common.primitives.IntsMethodsForWeb                                  : loaded 2 times (x 67B)
Class com.google.common.collect.Multimap                                              : loaded 2 times (x 66B)
Class org.gradle.api.internal.ClassPathRegistry                                       : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList              : loaded 2 times (x 195B)
Class com.google.common.base.ExtraObjectsMethodsForWeb                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatisticsUtilsKt$collectGeneralConfigurationTimeMetrics$statisticOverhead$1$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation$DefaultImpls               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt$providerWithLazyConvention$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$Companion$predefinedTargets$2     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$unstableFeaturesCheck$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl                    : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics$WasmWasiEnvironmentNotChosenExplicitly: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinNativeKlibArtifactKt$KotlinNativeKlibArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerOptionsDefault              : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerToolOptionsDefault          : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$isUseXcodeMessageStyleEnabled$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager_Decorated         : loaded 2 times (x 115B)
Class org.jetbrains.kotlin.gradle.report.BuildReportMode$Companion                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$VariantImplementationFactory: loaded 2 times (x 66B)
Class com.google.common.base.Equivalence$Identity                                     : loaded 2 times (x 78B)
Class com.google.common.io.ByteSink                                                   : loaded 2 times (x 72B)
Class com.google.common.base.PairwiseEquivalence                                      : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService             : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$1  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.SemVer$Companion                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$LINUX_ARM64                       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$LINUX_X64                         : loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.konan.target.Architecture;                               : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.targets.native.SetupEmbedAndSignAppleFrameworkTaskSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinCinteropCompatibility : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.dsl.ExplicitApiMode                                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.GradleAttributesContainerUtilsKt              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTopLevelExtensionConfig                   : loaded 2 times (x 66B)
Class com.google.common.cache.CacheBuilder                                            : loaded 2 times (x 68B)
Class com.google.common.collect.SingletonImmutableList                                : loaded 2 times (x 203B)
Class Settings_gradle$1                                                               : loaded 2 times (x 70B)
Class org.gradle.internal.classpath.ClassPath                                         : loaded 2 times (x 66B)
Class org.gradle.api.UncheckedIOException                                             : loaded 2 times (x 78B)
Class com.google.common.io.Files$FileByteSource                                       : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.incremental.IncrementalModuleInfoProvider           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$2  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$1 : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationProcessorSideEffectKt$KotlinCompilationProcessorSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptionsDefault                 : loaded 2 times (x 102B)
Class org.jetbrains.kotlin.gradle.internal.UsesClassLoadersCachingBuildService        : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableList$ReverseImmutableList                    : loaded 2 times (x 204B)
Class com.google.common.base.JdkPattern                                               : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.compilerRunner.KotlinCompilerArgumentsLogLevel$Companion   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$3$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$defaultKotlinJavaToolchain$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.daemon.common.MultiModuleICSettings                        : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.cli.common.arguments.Freezable                             : loaded 2 times (x 68B)
Class com.google.errorprone.annotations.DoNotMock                                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform$Parameters: loaded 2 times (x 83B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfoKt                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile                                 : loaded 2 times (x 494B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$MINGW_X64                         : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$2 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.utils.ObservableSet                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsCollectorKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.HierarchyAttributeContainer              : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.tasks.UsesKotlinJavaToolchain                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$apply$1              : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$OVERRIDE           : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Params_Decorated: loaded 2 times (x 130B)
Class org.objectweb.asm.SymbolTable$Entry                                             : loaded 2 times (x 70B)
Class com.google.common.util.concurrent.AbstractFuture$AtomicHelper                   : loaded 2 times (x 74B)
Class Native_plugin_loader_gradle                                                     : loaded 2 times (x 126B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformPluginBase                     : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetComponentWithCoordinatesAndPublication: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.incremental.UsesIncrementalModuleInfoBuildService   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$ANDROID_X86                       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$3 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.RegisterKotlinPluginExtensionsKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.resources.resolve.KotlinTargetResourcesResolutionStrategy: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.CompilerOptionsKt$configureExperimentalTryNext$1$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.utils.CompilerOptionsKt                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.SingleAction                                  : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$Companion                 : loaded 2 times (x 67B)
Class org.objectweb.asm.Edge                                                          : loaded 2 times (x 68B)
Class com.google.common.base.Suppliers$SupplierOfInstance                             : loaded 2 times (x 75B)
Class com.google.common.collect.Iterators$1                                           : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$defaultKotlinJavaToolchain$1    : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain$JavaToolchainSetter       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$4 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsLikeEnvironmentChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.UsesKotlinToolingDiagnostics     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics$WasmJsEnvironmentNotChosenExplicitly: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.tooling.core.ExtrasPropertyKt$extrasLazyProperty$1         : loaded 2 times (x 90B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy$SAFE         : loaded 2 times (x 81B)
Class com.google.common.cache.LocalCache$ComputingValueReference                      : loaded 2 times (x 92B)
Class com.google.common.collect.SingletonImmutableSet                                 : loaded 2 times (x 142B)
Class com.google.common.collect.ImmutableMap$IteratorBasedImmutableMap                : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationSourceSetsContainer$allKotlinSourceSetsImpl$1$1: loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationSourceSetsContainer: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$TVOS_SIMULATOR_ARM64              : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$Companion                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$Stage$Companion        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DeprecatedKotlinNativeTargetsChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.ConfigureFrameworkExportSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.tooling.core.AbstractExtras                                : loaded 2 times (x 137B)
Class com.google.common.cache.LocalCache$Segment                                      : loaded 2 times (x 150B)
Class Native_plugin_loader_gradle$NativePluginLoader$Companion                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTimes                            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ClasspathSnapshotProperties     : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2$1     : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.TransformActionUsingKotlinToolingDiagnostics: loaded 2 times (x 66B)
Class com.google.common.collect.SortedIterable                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$6 : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet_Decorated     : loaded 2 times (x 188B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.GradleUtilsKt$registerClassLoaderScopedBuildService$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy                    : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.DefaultKotlinBuildStatsBeanService: loaded 2 times (x 88B)
Class [Lorg.objectweb.asm.SymbolTable$Entry;                                          : loaded 2 times (x 65B)
Class com.google.common.cache.CacheLoader                                             : loaded 2 times (x 70B)
Class com.google.common.collect.Iterators$4                                           : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableBiMap                                        : loaded 2 times (x 139B)
Class com.google.common.collect.AbstractListMultimap                                  : loaded 2 times (x 168B)
Class org.gradle.internal.installation.GradleInstallation$1                           : loaded 2 times (x 71B)
Class org.gradle.api.internal.classpath.ModuleRegistry                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.logging.GradleKotlinLogger                          : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$ProjectConfigurationResult: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibDefaultDependency$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.model.builder.KotlinModelBuilder$Companion          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateResourcesTaskSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.SetupKotlinNativePlatformDependenciesAndStdlibKt: loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.mpp.resources.resolve.KotlinTargetResourcesResolutionStrategy;: loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$Companion: loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.GradleBuildPerformanceMetric;       : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsBeanService$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion         : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.internal.InternalFutureFailureAccess          : loaded 2 times (x 68B)
Class com.google.common.collect.Iterators$5                                           : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableSet$JdkBackedSetBuilderImpl                  : loaded 2 times (x 72B)
Class com.google.common.base.Splitter$SplittingIterator                               : loaded 2 times (x 80B)
Class build_25ymzt3t2d099vfqx1rsskgw0                                                 : loaded 2 times (x 176B)
Class com.google.common.io.Closer$SuppressingSuppressor                               : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.HasCompilerOptions                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver$FriendArtifactResolver: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_SIMULATOR_ARM64           : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.js.ir.KotlinJsIrTarget                      : loaded 2 times (x 256B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DefaultKotlinUsageContext                : loaded 2 times (x 104B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$start$3            : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinMultiplatformPluginWrapper             : loaded 2 times (x 82B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanMetrics;                       : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Companion: loaded 2 times (x 67B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper             : loaded 2 times (x 74B)
Class com.google.common.cache.LocalCache$LocalManualCache$1                           : loaded 2 times (x 71B)
Class com.google.common.base.CharMatcher$ForPredicate                                 : loaded 2 times (x 108B)
Class build_25ymzt3t2d099vfqx1rsskgw0$_run_closure1                                   : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationConfigurationsContainer: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$CoroutineStart;      : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.dsl.IosSourceSetConventionChecker                   : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectChecker       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinMultiplatformSourceSetConventions         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinSourceSetFactory$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy$SUM                : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableCollection                                   : loaded 2 times (x 121B)
Class com.google.common.base.CharMatcher$AnyOf                                        : loaded 2 times (x 108B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry                         : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.plugin.internal.state.TaskExecutionResults          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Parameters$Inject : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.compilerRunner.KotlinCompilerArgumentsLogLevel             : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.CompositePostConfigure: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.CInteropConfigurationsKt    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildTime$Companion             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.statistics.metrics.StatisticsValuesConsumer                : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.ListenableFuture                              : loaded 2 times (x 66B)
Class com.google.common.cache.ReferenceEntry                                          : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableSet$CachingAsList                            : loaded 2 times (x 143B)
Class com.google.common.base.Converter                                                : loaded 2 times (x 86B)
Class com.google.common.collect.Lists                                                 : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$Any                                          : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$2$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.NonSynchronizedMetricsContainer   : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.CompilerPluginConfig                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.model.builder.KotlinModelBuilder                    : loaded 2 times (x 72B)
Class [Lorg.jetbrains.kotlin.tooling.core.HasMutableExtras;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.GradleDeprecatedPropertyChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinJsKlibArtifactKt$KotlinJsKlibArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.CreateTargetConfigurationsSideEffectKt      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.ApplyUserDefinedAttributesKt$UserDefinedAttributesSetupAction$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinAndroidTarget                      : loaded 2 times (x 161B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultCompatibilityConventionRegistrar: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider                           : loaded 2 times (x 68B)
Class com.google.common.util.concurrent.AbstractFuture                                : loaded 2 times (x 102B)
Class com.google.common.base.FunctionalEquivalence                                    : loaded 2 times (x 79B)
Class com.google.common.collect.Iterators$9                                           : loaded 2 times (x 77B)
Class com.google.common.collect.AbstractMapBasedMultimap                              : loaded 2 times (x 135B)
Class com.google.common.collect.ImmutableAsList                                       : loaded 2 times (x 205B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.jvm.JvmTargetValidationMode;                  : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$javaVersion$1      : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.MppSourcesJarKt$sourcesJarTask$1         : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationOutputFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.TargetSupportException                        : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationToRunnableFiles             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTaskKt            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$setupAttributesMatchingStrategy$1$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.BuildSessionLogger$Companion                    : loaded 2 times (x 67B)
Class org.objectweb.asm.RecordComponentVisitor                                        : loaded 2 times (x 73B)
Class com.google.common.cache.CacheLoader$SupplierToCacheLoader                       : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.SemverKt                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$isStdlibAddedByUser$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$sam$java_util_concurrent_Callable$0: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerPluginData                      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.MppSourcesJarKt                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfo                        : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.tasks.BaseKotlinCompile                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.internal.UsesBuildIdProviderService          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$include$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilationDependencyConfigurationsFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleCoroutineContextElement$Key: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.IdeaKt                                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsSetupActionKt$KotlinToolingDiagnosticsSetupAction$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.tooling.core.ExtrasPropertyKt                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtensionKt                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$3: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.IMetricContainerFactory;              : loaded 2 times (x 65B)
Class com.google.common.collect.Maps$EntryTransformer                                 : loaded 2 times (x 66B)
Class org.gradle.internal.service.ServiceLookupException                              : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Inject            : loaded 2 times (x 100B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$isStdlibAddedByUser$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.tasks.GradleCompileTaskProvider_Decorated           : loaded 2 times (x 117B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifierKt$sourceSetTreeClassifier$2: loaded 2 times (x 74B)
Class com.google.common.collect.ImmutableSortedSet                                    : loaded 2 times (x 295B)
Class org.jetbrains.kotlin.konan.target.HostManager                                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDirectoryDependencyExtension      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsageContext$MavenScope            : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.native.KotilnNativeConfigureBinariesSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupAction$Companion           : loaded 2 times (x 67B)
Class [Lcom.google.common.cache.LocalCache$EntryFactory;                              : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$JavaLetter                                   : loaded 2 times (x 107B)
Class org.gradle.internal.classloader.ClassLoaderFactory                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$currentJvmJdkToolsJar$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.SubpluginEnvironment                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.sources.AbstractKotlinSourceSet              : loaded 2 times (x 137B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTaskKt$RegisterBuildKotlinToolingMetadataTask$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.compilerRunner.GradleCliCommonizerKt$maybeCreateCommonizerClasspathConfiguration$1$1: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.ValueType;                          : loaded 2 times (x 65B)
Class com.google.common.cache.LocalCache$LocalManualCache                             : loaded 2 times (x 95B)
Class com.google.common.base.CharMatcher$IsNot                                        : loaded 2 times (x 107B)
Class com.google.common.base.CommonPattern                                            : loaded 2 times (x 70B)
Class org.gradle.internal.service.CachingServiceLocator                               : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.dsl.jvm.JvmTargetValidationMode                     : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService$Companion$registerIfAbsent$2$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetConfiguratorKt$WhenMappings      : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet                       : loaded 2 times (x 133B)
Class com.google.common.collect.RegularImmutableSortedSet                             : loaded 2 times (x 295B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt$configureKotlinTestDependency$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Params_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.project.model.LanguageSettings                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleKt$kotlinPluginLifecycle$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion$registerIfAbsent$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishFlowAction$Parameters_Decorated   : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CompatibilityConventionRegistrar$Factory: loaded 2 times (x 66B)
Class com.google.common.math.IntMath                                                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinMetadataTarget                     : loaded 2 times (x 163B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt$locateOrRegisterCheckKotlinGradlePluginErrorsTask$taskProvider$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilation$DefaultImpls   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$DefaultAddSourcesToCompileTask: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt$configureKotlinTestDependency$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$runChecks$$inlined$CheckedPlatformInfo$default$1: loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$Stage;               : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinTargetArtifact$Companion            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateLifecycleTasksSideEffectKt$KotlinCreateLifecycleTasksSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt$metadataCompilationsCreated$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinGradlePluginDsl                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.tooling.core.MutableExtras                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$detectKotlinPluginLoadedInMultipleProjects$onRegister$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTime                             : loaded 2 times (x 66B)
Class com.google.common.collect.Iterators$10                                          : loaded 2 times (x 77B)
Class com.google.common.collect.ImmutableMapEntry                                     : loaded 2 times (x 81B)
Class com.google.common.base.CharMatcher$JavaDigit                                    : loaded 2 times (x 107B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt$locateOrRegisterCheckKotlinGradlePluginErrorsTask$taskProvider$1$2: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.CompileUsingKotlinDaemon                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinWithJavaTarget                     : loaded 2 times (x 162B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateNativeCompileTasksSideEffectKt$special$$inlined$KotlinCompilationSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.KotlinTargetSideEffect$Companion            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinSingleJavaTargetExtension                 : loaded 2 times (x 120B)
Class org.jetbrains.kotlin.gradle.utils.StoredPropertyKt$extrasStoredProperty$1       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$IllegalLifecycleException: loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$userProvidedNativeHome$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleFinishBuildHandler               : loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetric;             : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$CONCAT             : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTopLevelExtension                         : loaded 2 times (x 66B)
Class com.google.common.base.Equivalence                                              : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableMapEntry$NonTerminalImmutableMapEntry        : loaded 2 times (x 81B)
Class com.google.common.base.Function                                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurationsKt                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDomApiDependencyManagementKt$addKotlinDomApiDependency$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KotlinTargetAlreadyDeclaredChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetWithBinaries                 : loaded 2 times (x 159B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinSourceSet;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsEnvironmentChecker$1  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics$JsLikeEnvironmentNotChosenExplicitly: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetsContainerWithPresets            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.StoredLazyProperty$getValue$1                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.IMetricContainerFactory                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.MetricValueValidationFailed                     : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$localProperties$2         : loaded 2 times (x 75B)
Class com.google.common.collect.IndexedImmutableSet$1                                 : loaded 2 times (x 208B)
Class com.google.common.cache.CacheBuilder$OneWeigher                                 : loaded 2 times (x 78B)
Class com.google.common.collect.RegularImmutableList                                  : loaded 2 times (x 208B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver$resolveFriendPaths$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.build.report.metrics.BuildMetricsReporterImpl              : loaded 2 times (x 94B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.KaptGenerateStubsConfig         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$classpathConfiguration$jvmToolchain$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.SemVer                               : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsEnvironmentChecker$2  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.utils.WhenPluginEnabledKt$whenPluginsEnabled$1      : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompileTool                     : loaded 2 times (x 362B)
Class [Lorg.jetbrains.kotlin.gradle.report.BuildReportType;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsConfiguration     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$localProperties$2: loaded 2 times (x 75B)
Class com.google.common.collect.ImmutableBiMapFauxverideShim                          : loaded 2 times (x 116B)
Class org.gradle.internal.classloader.ClasspathUtil                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCompile                                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver$DefaultFriendArtifactResolver: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.InternalGradlePropertiesUsageChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.utils.StringUtilsKt$lowerCamelCaseName$1            : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.JsEnvironmentChecker$3  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinLegacyMetadataArtifactKt            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.JvmTarget$Companion                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$downloadFromMaven$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultConfigurationCacheStartParameterAccessor_Decorated: loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultConfigurationCacheStartParameterAccessor: loaded 2 times (x 73B)
Class org.jetbrains.kotlin.tooling.core.HasExtras                                     : loaded 2 times (x 66B)
Class com.google.common.base.Charsets                                                 : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.Uninterruptibles                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationInfo$TCS$friendPaths$1      : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultLanguageSettingsBuilder$freeCompilerArgsProvider$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.konan.target.HostManager$Companion                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DevNpmDependencyExtension            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.PreHmppDependenciesUsageChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.targets.CreateArtifactsSideEffectKt                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinMultiplatformPluginWrapper     : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.utils.GradleConfigurationUtilsKt$addGradlePluginMetadataAttributes$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultProjectIsolationStartParameterAccessor: loaded 2 times (x 73B)
Class com.google.common.collect.Maps                                                  : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$JavaUpperCase                                : loaded 2 times (x 107B)
Class org.gradle.api.specs.Spec                                                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$disableMultiModuleIC$2          : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.GradleCompileTaskProviderKt                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.SubpluginEnvironment$Companion               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.ApplyUserDefinedAttributesKt$UserDefinedAttributesSetupAction$1$1$3: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$Params: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.konan.target.UtilsKt                                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTask              : loaded 2 times (x 313B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CompatibilityConventionRegistrarKt  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinJvmJarArtifactKt                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.CreateCInteropTasksSideEffectKt$special$$inlined$KotlinCompilationSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FinalizeConfigurationFusMetricActionKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.resources.resolve.KotlinTargetResourcesResolutionStrategy$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.toolchain.KotlinNativeBundleBuildService$Companion$registerIfAbsent$2$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.build.report.metrics.GradleBuildPerformanceMetric$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsBeanService       : loaded 2 times (x 83B)
Class [Lcom.google.common.collect.ImmutableEntry;                                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$1 : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$kotlinOptions$1                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativeProperties$Companion      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.StringUtilsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.ConfigureBuildSideEffectKt$ConfigureBuildSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.tooling.core.ExtrasLazyProperty                            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.CompletableFuture                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetric                : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.StringOverridePolicy;                 : loaded 2 times (x 65B)
Class com.google.common.cache.LocalCache$LoadingValueReference                        : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Inject             : loaded 2 times (x 96B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$getClasspathSnapshotDir$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$2 : loaded 2 times (x 70B)
Class com.google.common.collect.Multimaps$Entries                                     : loaded 2 times (x 114B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Params$Inject: loaded 2 times (x 102B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.CommonMainOrTestWithDependsOnChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinNativeHostSpecificMetadataArtifactKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateLifecycleTasksSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTarget                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt$createResolvable$1           : loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy;           : loaded 2 times (x 65B)
Class com.google.common.base.Platform                                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.Completable$await$1                           : loaded 2 times (x 84B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedList                  : loaded 2 times (x 201B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$allNonProjectDependencies$1: loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildAttributeKind;                 : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformStructure                      : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Parameters  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetsContainer: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinNativeKlibArtifactKt                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinUsagesDisambiguation  : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.tooling.core.Extras$Key$Companion                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.Extras$Key                                    : loaded 2 times (x 68B)
Class [Lorg.objectweb.asm.AnnotationWriter;                                           : loaded 2 times (x 65B)
Class com.google.common.primitives.Ints$IntConverter                                  : loaded 2 times (x 86B)
Class com.google.common.collect.Maps$8                                                : loaded 2 times (x 78B)
Class Build_gradle$3                                                                  : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$4 : loaded 2 times (x 71B)
Class com.google.common.collect.ImmutableRangeSet$Builder                             : loaded 2 times (x 73B)
Class [Lcom.google.common.collect.Iterators$EmptyModifiableIterator;                  : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrors: loaded 2 times (x 313B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmOptions                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.HierarchyAttributeContainer$1            : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJsPluginWrapper                        : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.dsl.ToolchainSupport                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.tooling.core.Extras                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTarget                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy$RANDOM_10_PERCENT: loaded 2 times (x 81B)
Class org.jetbrains.kotlin.statistics.metrics.StringMetrics                           : loaded 2 times (x 76B)
Class com.google.common.collect.Sets$SetView                                          : loaded 2 times (x 134B)
Class com.google.common.cache.LocalCache$StrongValueReference                         : loaded 2 times (x 86B)
Class com.google.common.util.concurrent.AbstractFuture$SynchronizedHelper             : loaded 2 times (x 74B)
Class com.google.common.base.CharMatcher$Invisible                                    : loaded 2 times (x 108B)
Class com.google.common.graph.SuccessorsFunction                                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.CompilerPluginOptions                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$5 : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImpl        : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DefaultKotlinUsageContext$PublishOnlyIf  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerOptionsHelper               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJvmPlugin$Companion                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinMetadataArtifactKt                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.CreateArtifactsSideEffectKt$CreateArtifactsSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$property$1$valueFromGradleAndLocalProperties$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt$kotlinToolingVersion$2 : loaded 2 times (x 74B)
Class org.objectweb.asm.Type                                                          : loaded 2 times (x 68B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: loaded 2 times (x 119B)
Class com.google.common.io.Files$FileByteSink                                         : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$6 : loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy;            : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.FailedCompilationException                    : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DefaultKotlinUsageContext$1              : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl$associateWith$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.hierarchy.KotlinSourceSetTreeClassifier      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationAssociator: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.UtilsKt$evaluatePresetName$1                  : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinNativeTarget                       : loaded 2 times (x 169B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.KotlinSourceSetTreeDependsOnMismatchChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.PreHmppDependenciesUsageChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.utils.ProjectExtensionsKt                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSetTree$Companion                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectChecker$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Companion$registerIfAbsent$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.fileloggers.MetricsContainer$Companion          : loaded 2 times (x 67B)
Class org.objectweb.asm.ByteVector                                                    : loaded 2 times (x 75B)
Class [Lcom.google.common.cache.CacheBuilder$NullListener;                            : loaded 2 times (x 65B)
Class com.google.common.math.IntMath$1                                                : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$Spec                    : loaded 2 times (x 70B)
Class org.gradle.internal.classpath.DefaultClassPath                                  : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinSoftwareComponentKt                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$AddSourcesToCompileTask: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.Architecture                                  : loaded 2 times (x 75B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.HasProject;                                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.utils.MutableObservableSetImpl                      : loaded 2 times (x 159B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinTasksProvider                           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJsCompilerTypeHolder                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport$wireToolchainToTasks$1  : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerOptions                     : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.GradleBuildTime;                    : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$MemoizedCallable: loaded 2 times (x 71B)
Class org.objectweb.asm.Symbol                                                        : loaded 2 times (x 69B)
Class com.google.common.base.AbstractIterator                                         : loaded 2 times (x 76B)
Class com.google.common.io.Closer                                                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$friendPathsSet$1        : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributes                       : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.cocoapods.KotlinCocoapodsPlugin$Companion    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationImpl    : loaded 2 times (x 136B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PostConfigure;: loaded 2 times (x 65B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$MACOS_X64                         : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.util.Named                                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSet$Companion                    : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.report.BuildReportMode;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy                   : loaded 2 times (x 81B)
Class com.google.common.collect.RegularImmutableMap$Values                            : loaded 2 times (x 203B)
Class com.google.common.collect.ImmutableSet                                          : loaded 2 times (x 141B)
Class com.google.common.base.CharMatcher$BitSetMatcher                                : loaded 2 times (x 108B)
Class com.google.common.base.CharMatcher$Ascii                                        : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinAndroidPluginWrapper           : loaded 2 times (x 82B)
Class org.gradle.internal.classloader.ClassLoaderSpec                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJavaToolchainSetter: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributeKind                    : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$moduleNameForCompilation$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScopeKt              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.TransformActionUsingKotlinToolingDiagnostics$Parameters: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion$registerIfAbsent$2$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$addSourcesToKotlinCompileTask$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.KotlinExtensionUtilsKt$forAllTargets$1        : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.CheckKotlinGradlePluginConfigurationErrorsKt$locateOrRegisterCheckKotlinGradlePluginErrorsTask$taskProvider$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Params: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$nativeTargetPresets$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTaskKt$buildKotlinToolingMetadataTask$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin$Companion             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateCompilationArchiveTaskKt$KotlinRegisterCompilationArchiveTasksExtension$1: loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy;                 : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$GradleProperty: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.StoredPropertyKt$projectStoredProperty$1      : loaded 2 times (x 74B)
Class org.objectweb.asm.AnnotationWriter                                              : loaded 2 times (x 75B)
Class com.google.common.cache.CacheStats                                              : loaded 2 times (x 67B)
Class com.google.common.io.Closer$Suppressor                                          : loaded 2 times (x 66B)
Class com.google.common.io.LineProcessor                                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformStructure$RefinesEdge          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.SyncLanguageSettingsWithKotlinExtensionSetupActionKt$SyncLanguageSettingsWithKotlinExtensionSetupAction$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.TaskConfigAction$configureTask$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DuplicateSourceSetChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnostic$Severity       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.dsl.JvmTarget                                       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService            : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Inject        : loaded 2 times (x 102B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactoriesConfigurator$Companion: loaded 2 times (x 67B)
Class org.objectweb.asm.RecordComponentWriter                                         : loaded 2 times (x 74B)
Class com.google.common.util.concurrent.AbstractFuture$Listener                       : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$Or                                           : loaded 2 times (x 108B)
Class Settings_gradle$1$1                                                             : loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.compilerRunner.KotlinCompilerArgumentsLogLevel;          : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile_Decorated                       : loaded 2 times (x 595B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.SyncLanguageSettingsWithKotlinExtensionSetupActionKt$SyncLanguageSettingsWithKotlinExtensionSetupAction$1$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.BuildIdService$Companion            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmSubTargetContainerDsl      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmWasiEnvironmentChecker$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DisabledCinteropCommonizationInHmppProjectChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.NoKotlinTargetsDeclaredChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.StoredPropertyStorage                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSetContainer                     : loaded 2 times (x 66B)
Class org.objectweb.asm.Attribute                                                     : loaded 2 times (x 73B)
Class com.google.common.base.Supplier                                                 : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.VisitableURLClassLoader                         : loaded 2 times (x 113B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.ApplyUserDefinedAttributesKt$UserDefinedAttributesSetupAction$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.plugins.ObservablePlugins         : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.InternalKotlinSourceSetKt$awaitPlatformCompilations$1: loaded 2 times (x 84B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope;             : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$configureCommonParameters$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.ConfigurationNaming: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationPostConfigureKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_X64                       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectCheckersRunnerKt: loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.tooling.core.HasExtras;                                  : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.IncorrectCompileOnlyDependenciesChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmWasiEnvironmentChecker$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinLegacyCompatibilityMetadataArtifactKt$KotlinLegacyCompatibilityMetadataArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport$wireToolchainToTasks$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.SingleActionPerProject                        : loaded 2 times (x 69B)
Class com.google.common.util.concurrent.AbstractFuture$Waiter                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin                      : loaded 2 times (x 78B)
Class org.gradle.internal.Cast                                                        : loaded 2 times (x 67B)
Class org.gradle.api.Action                                                           : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsageContext$MavenScope;         : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$configureCommonParameters$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationCompilerOptionsFromTargetConfigurator: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.FailuresKt$ideaSyncClasspathModeUtil$1        : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsCollector$Inject: loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinJsKlibArtifactKt                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithWasmPresetFunctions    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.GradleUtilsKt                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService          : loaded 2 times (x 72B)
Class com.google.common.collect.Sets                                                  : loaded 2 times (x 67B)
Class org.objectweb.asm.Handler                                                       : loaded 2 times (x 68B)
Class com.google.common.base.Ticker                                                   : loaded 2 times (x 68B)
Class com.google.common.base.Strings                                                  : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$FastMatcher                                  : loaded 2 times (x 107B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$javaSources$1                   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.incremental.IncrementalCompilationFeatures                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTarget$DefaultImpls                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnosticsCollector: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateCompilationArchiveTaskKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.MutableExtrasImpl                             : loaded 2 times (x 161B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsMXBean            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.FileUtilsKt                                   : loaded 2 times (x 67B)
Class [Lcom.google.common.cache.LocalCache$Segment;                                   : loaded 2 times (x 65B)
Class [Lcom.google.common.cache.LocalCache$Strength;                                  : loaded 2 times (x 65B)
Class com.google.common.cache.CacheBuilder$1                                          : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableMap                                          : loaded 2 times (x 116B)
Class com.google.common.base.Predicate                                                : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList$Builder      : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationFactory$DefaultImpls    : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractRangeSet                                      : loaded 2 times (x 110B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.InternalGradlePropertiesUsageChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.CreateCInteropTasksSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.testing.internal.KotlinTestsRegistry$Companion      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerToolOptions                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Companion$registerIfAbsent$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultConfigurationCacheStartParameterAccessorVariantFactory: loaded 2 times (x 72B)
Class com.google.common.cache.LocalCache                                              : loaded 2 times (x 183B)
Class com.google.common.cache.CacheBuilder$2                                          : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$NegatedFastMatcher                           : loaded 2 times (x 109B)
Class Build_gradle$inlined$sam$i$org_gradle_api_Action$0                              : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetrics$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.arguments.K2JVMCompilerArguments                : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationSourceSetsContainer$source$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$ANDROID_X64                       : loaded 2 times (x 74B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.ConsistencyCheck;                  : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.report.ReportingSettings                            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$ComponentVersionAnonymizer: loaded 2 times (x 74B)
Class [Lcom.google.common.collect.AbstractMapEntry;                                   : loaded 2 times (x 65B)
Class com.google.common.collect.ImmutableEntry                                        : loaded 2 times (x 78B)
Class com.google.common.collect.ArrayListMultimap                                     : loaded 2 times (x 168B)
Class com.google.common.base.AbstractIterator$1                                       : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$Digit                                        : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinBasePluginWrapper                      : loaded 2 times (x 82B)
Class org.gradle.api.GradleException                                                  : loaded 2 times (x 78B)
Class com.google.common.base.Suppliers$MemoizingSupplier                              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinToolingDiagnostics$JsEnvironmentNotChosenExplicitly: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$ProjectLocalCompatibility: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptions                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultProjectIsolationStartParameterAccessor$Factory: loaded 2 times (x 72B)
Class com.google.common.collect.CollectPreconditions                                  : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$JavaIsoControl                               : loaded 2 times (x 108B)
Class [Lcom.google.common.io.FileWriteMode;                                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.buildtools.api.SourcesChanges                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.HostManager$Companion$targetAliases$2         : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$launch$1           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.Completable                                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$2$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsServicesRegistry$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultCompatibilityConventionRegistrar$Factory: loaded 2 times (x 72B)
Class com.google.common.collect.SortedMapDifference                                   : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$JavaLowerCase                                : loaded 2 times (x 107B)
Class Build_gradle$1$1                                                                : loaded 2 times (x 74B)
Class com.google.common.io.Files                                                      : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$WriteThroughEntry                            : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$HashIterator                                 : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJdkSetter   : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.cli.common.arguments.CommonCompilerArguments               : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.IncorrectCompileOnlyDependenciesChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinOnlyTarget                         : loaded 2 times (x 155B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.BaseNpmDependencyExtension           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinVersion$Companion                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.InternalKotlinGradlePluginApi                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport                         : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource     : loaded 2 times (x 78B)
Class com.google.common.cache.LocalCache$EntryFactory                                 : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.tasks.TaskOutputsBackup                             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.cocoapods.KotlinCocoapodsPlugin              : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.internal.CompilerOptionsDslHelpersKt                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtension$awaitSourceSets$1        : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.konan.target.Family                                        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycle$CoroutineStart         : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.TasksProviderKt$sam$org_gradle_api_Action$0   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTask$FromKotlinExtension: loaded 2 times (x 313B)
Class org.jetbrains.kotlin.gradle.utils.FailuresKt                                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.CInteropConfigurationsKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$konanDataDirProperty$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.ClassLoadersCachingBuildService$Companion$registerIfAbsent$1$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService$Parameters_Decorated: loaded 2 times (x 139B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Params$Inject: loaded 2 times (x 102B)
Class org.jetbrains.kotlin.tooling.core.HasMutableExtras                              : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$AbstractReferenceEntry                       : loaded 2 times (x 103B)
Class com.google.common.base.Splitter$1$1                                             : loaded 2 times (x 82B)
Class com.google.common.base.CharMatcher$Whitespace                                   : loaded 2 times (x 108B)
Class org.gradle.internal.IoActions                                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3KotlinGradleSubpluginKt               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompile                                : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableRangeSet$ComplementRanges                    : loaded 2 times (x 203B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$LINUX_ARM32_HFP                   : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin                       : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.CInteropInputChecker    : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FinalizeConfigurationFusMetricActionKt$FinalizeConfigurationFusMetricAction$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$CustomizeKotlinDependenciesSetupAction$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradlePluginExtensionPointInternal$registeredExtensions$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy$OR                : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.UsesVariantImplementationFactories           : loaded 2 times (x 66B)
Class org.objectweb.asm.SymbolTable                                                   : loaded 2 times (x 68B)
Class com.google.common.cache.CacheLoader$InvalidCacheLoadException                   : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableSet$RegularSetBuilderImpl                    : loaded 2 times (x 73B)
Class com.google.common.base.CharMatcher$SingleWidth                                  : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.SystemClassLoaderSpec                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.CacheableTasksKt$cacheOnlyIfEnabledForKotlin$1$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$1     : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$PostConfigure: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.KotlinExtensionUtilsKt                        : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.konan.util.Named;                                        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.NoKotlinTargetsDeclaredChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget$kotlinComponents$2  : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$CompatibilityRule         : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$actualNativeHomeDirectory$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesKt              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.BuildEventsListenerRegistryHolder            : loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy;                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource$Parameters$Inject: loaded 2 times (x 102B)
Class com.google.common.cache.LocalCache$StrongEntry                                  : loaded 2 times (x 104B)
Class com.google.common.util.concurrent.AbstractFuture$TrustedFuture                  : loaded 2 times (x 110B)
Class com.google.common.cache.LoadingCache                                            : loaded 2 times (x 66B)
Class com.google.common.io.ByteStreams$LimitedInputStream                             : loaded 2 times (x 88B)
Class com.google.common.base.Suppliers$NonSerializableMemoizingSupplier               : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.GradleCompileTaskProvider                     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImplKt$KotlinTargetSoftwareComponent$1: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$registerBuildToolsApiTransformations$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.BuildIdService                      : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationDependencyConfigurationsFactoriesKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.DefaultKotlinCompilationFriendPathsResolver: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmSourceSetsNotFoundChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$IOS_SIMULATOR_ARM64               : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateCompilationArchiveTaskKt$KotlinCreateCompilationArchivesTask$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinVersion                                   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$actualNativeHomeDirectory$2: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.StoredPropertyKt                              : loaded 2 times (x 67B)
Class org.objectweb.asm.MethodWriter                                                  : loaded 2 times (x 102B)
Class [Lcom.google.common.cache.Weigher;                                              : loaded 2 times (x 65B)
Class com.google.common.collect.RegularImmutableMap$BucketOverflowException           : loaded 2 times (x 78B)
Class com.google.common.collect.AbstractMapBasedMultimap$RandomAccessWrappedList      : loaded 2 times (x 201B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope$Companion      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleKt$launchInStage$1      : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$3     : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$registerBuildToolsApiTransformations$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJvmCompile                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetsContainerKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.NativeForwardImplementationToApiElementsSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$actualNativeHomeDirectory$3: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.report.BuildReportMode                              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$RegexControlled: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.ValueAnonymizer                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion$getProvider$2$1$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ProjectIsolationStartParameterAccessor$Factory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$disableMultiModuleIC$2$1        : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$javaExecutable$1   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttribute                        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.transforms.BuildToolsApiClasspathEntrySnapshotTransform$Inject1: loaded 2 times (x 109B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationRegisterInSourceSetsConfigurator: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.DefaultKotlinCompilationTaskNamesContainerFactory: loaded 2 times (x 70B)
Class com.google.common.collect.RegularImmutableBiMap                                 : loaded 2 times (x 144B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.CommonizerTasksKt           : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency$Scope;               : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilerPluginSupportPlugin            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget$components$2        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$setupAttributesMatchingStrategy$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.AndroidPluginIdsKt                            : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$ValueReference                               : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$Negated                                      : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$Is                                           : loaded 2 times (x 107B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinBasePlugin                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.internal.KotlinJvmOptionsCompat               : loaded 2 times (x 99B)
Class org.jetbrains.kotlin.cli.common.arguments.CommonToolArguments                   : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.utils.FutureKt                                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.logging.GradleLoggingUtilsKt                        : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.AbstractFuture$Failure                        : loaded 2 times (x 68B)
Class com.google.common.base.Equivalence$Equals                                       : loaded 2 times (x 78B)
Class com.google.common.collect.Hashing                                               : loaded 2 times (x 67B)
Class com.google.common.base.PatternCompiler                                          : loaded 2 times (x 66B)
Class com.google.common.base.Preconditions                                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinAndroidPluginWrapper                   : loaded 2 times (x 82B)
Class com.google.common.io.CharSink                                                   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunner$Companion              : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.PlatformSourceSetConventionsChecker$CheckedPlatformInfo;: loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.KotlinGradleProjectCheckerContext: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DuplicateSourceSetChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnosticFactory         : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.targets.KotlinTargetSideEffect                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithNativeShortcuts        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$setupAttributeMatchingStrategy$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.NumericalMetrics$Companion              : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.NumberAnonymizationPolicy;            : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy$OVERRIDE           : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$property$1: loaded 2 times (x 75B)
Class com.google.common.base.Joiner$1                                                 : loaded 2 times (x 76B)
Class org.objectweb.asm.Label                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.CurrentFrame                                                  : loaded 2 times (x 69B)
Class org.objectweb.asm.ClassTooLargeException                                        : loaded 2 times (x 79B)
Class com.google.common.collect.PeekingIterator                                       : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher                                              : loaded 2 times (x 107B)
Class com.google.common.collect.Maps$IteratorBasedAbstractMap                         : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$2$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributes$Companion             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilationTask                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$ProcessResourcesTaskNameFactory: loaded 2 times (x 66B)
Class com.google.common.collect.RangeGwtSerializationDependencies                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$property$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$setupAttributeMatchingStrategy$1$2: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsLoggerService     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource$Inject: loaded 2 times (x 109B)
Class com.google.common.base.Joiner$2                                                 : loaded 2 times (x 75B)
Class org.objectweb.asm.Frame                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.MethodVisitor                                                 : loaded 2 times (x 101B)
Class com.google.common.cache.LocalCache$EntryFactory$1                               : loaded 2 times (x 79B)
Class [Lcom.google.common.cache.RemovalListener;                                      : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$1                                            : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$NamedFastMatcher                             : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetKt$special$$inlined$extrasStoredFuture$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.compilerRunner.UsesCompilerSystemPropertiesService         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion$Companion: loaded 2 times (x 67B)
Class com.google.common.collect.RangeSet                                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_DEVICE_ARM64              : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.MissingNativeStdlibChecker$runChecks$1: loaded 2 times (x 84B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.android.AndroidVariantType;        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.sources.android.AndroidVariantType           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponent            : loaded 2 times (x 85B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinVariant                            : loaded 2 times (x 97B)
Class org.jetbrains.kotlin.gradle.targets.native.ConfigureFrameworkExportSideEffectKt : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.WhenPluginEnabledKt                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.ExtrasProperty                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories               : loaded 2 times (x 71B)
Class com.google.common.cache.LocalCache$EntryFactory$2                               : loaded 2 times (x 79B)
Class org.gradle.api.internal.classpath.GlobalCacheRootsProvider                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.tooling.core.Extras$Entry$Companion                        : loaded 2 times (x 67B)
Class com.google.common.collect.CollectCollectors                                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmWasiTargetDsl              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DisabledNativeTargetsChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCreateSourcesJarTaskSideEffectKt$KotlinCreateSourcesJarTaskSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.internal.CompilerArgumentAware                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ReportUtilsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.StoredLazyProperty                            : loaded 2 times (x 71B)
Class org.objectweb.asm.ModuleWriter                                                  : loaded 2 times (x 78B)
Class com.google.common.cache.LocalCache$EntryFactory$3                               : loaded 2 times (x 79B)
Class com.google.common.base.Ticker$1                                                 : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMapEntrySet$RegularEntrySet                  : loaded 2 times (x 147B)
Class com.google.common.collect.Maps$BiMapConverter                                   : loaded 2 times (x 86B)
Class Native_plugin_loader_gradle$NativePluginLoader                                  : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformStructure_Decorated            : loaded 2 times (x 131B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunner                        : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatisticsUtilsKt      : loaded 2 times (x 67B)
Class com.google.common.collect.SetMultimap                                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.AndroidMainSourceSetConventionsChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetComponent            : loaded 2 times (x 89B)
Class org.jetbrains.kotlin.gradle.targets.CreateDefaultTestRunSideEffectKt$special$$inlined$KotlinTargetSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.properties.NativePropertiesLoader$forceDisableRunningInProcess$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.PersistentCachesKt                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.tasks.TaskWithLocalState                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanMetrics                          : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.internal.properties.PropertiesBuildService$Params   : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$4                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$Builder                                  : loaded 2 times (x 81B)
Class com.google.common.base.CharMatcher$InRange                                      : loaded 2 times (x 107B)
Class com.google.common.io.ByteStreams$1                                              : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScopeKt$WhenMappings : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformCompilationTask                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker$StdlibExistenceCheckerValueSource$Inject: loaded 2 times (x 105B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationsKt$maybeCreateDependencyScope$1 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$languageVersionCheck$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DecoratedKotlinCompilation               : loaded 2 times (x 187B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.ExperimentalTryNextUsageChecker: loaded 2 times (x 70B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.diagnostics.ToolingDiagnostic$Severity;    : loaded 2 times (x 65B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinPlatformType;                        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.UsesBuildFusService               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.old.Pre232IdeaKotlinBuildStatsBeanService: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy$AVERAGE            : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Parameters$Inject: loaded 2 times (x 102B)
Class com.google.common.cache.LocalCache$EntryFactory$5                               : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.MppSourcesJarKt$sourcesJarTaskNamed$result$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.UsesBuildFinishedListenerService             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.ConfigurationNaming$Default: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.KotlinCompilationSourceSetInclusion: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.jvm.KotlinJvmTarget                         : loaded 2 times (x 168B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmJsTargetDsl                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.DisabledNativeTargetsChecker$runChecks$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginLifecycleImpl$await$2$1          : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultLanguageSettingsBuilder       : loaded 2 times (x 96B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$languageVersionCheck$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.CreateTargetConfigurationsSideEffectKt$CreateTargetConfigurationsSideEffect$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithPresetFunctions        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.GradleConfigurationUtilsKt                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.StatisticsBuildFlowManager$subscribeForBuildResult$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CustomPropertiesFileValueSource$Parameters: loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$6                               : loaded 2 times (x 79B)
Class org.gradle.internal.installation.CurrentGradleInstallationLocator               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$getToolsJarFromJvm$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.GcMetrics                             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.UsesKotlinToolingDiagnosticsKt   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishedListenerService$Companion$registerIfAbsent$2$1$1: loaded 2 times (x 71B)
Class com.google.common.collect.Maps$KeySet                                           : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$IOS_X64                           : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilation                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.WhenEvaluatedKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.AddKotlinPlatformIntegersSupportLibraryKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinJsPluginWrapper                : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt                        : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$EntryFactory$7                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$EmptySetBuilderImpl                      : loaded 2 times (x 72B)
Class com.google.common.collect.ImmutableList                                         : loaded 2 times (x 202B)
Class org.gradle.internal.service.DefaultServiceLocator                               : loaded 2 times (x 79B)
Class com.google.common.collect.Maps$EntrySet                                         : loaded 2 times (x 132B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$scriptExtensions$1              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.K2MultiplatformStructure$Fragment             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.util.capitalizeDecapitalize.CapitalizeDecapitalizeKt       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.WasmJsEnvironmentChecker: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.AndroidPluginWithoutAndroidTargetChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.statistics.metrics.NumberOverridePolicy                    : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy$AllowedListAnonymizer$Companion: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultProjectIsolationStartParameterAccessor_Decorated: loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ProjectIsolationStartParameterAccessorKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.testing.internal.KotlinTestsRegistry                : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$EntryFactory$8                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$SetBuilderImpl                           : loaded 2 times (x 72B)
Class org.gradle.internal.installation.CurrentGradleInstallation                      : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy               : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilationKt$awaitAllKotlinSourceSets$1: loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.FusMetrics                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet$special$$inlined$Iterable$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$KotlinCompilerOptionsFactory$Options: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation                            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetConfiguratorKt                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.tooling.core.Extras$Type$Companion                         : loaded 2 times (x 67B)
Class com.google.common.cache.CacheLoader$1                                           : loaded 2 times (x 71B)
Class com.google.common.collect.ImmutableList$SubList                                 : loaded 2 times (x 204B)
Class org.gradle.util.internal.GUtil                                                  : loaded 2 times (x 67B)
Class com.google.common.io.ByteSource                                                 : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinTargetKt$isSourcesPublishableFuture$2: loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationFactory                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.IosSourceSetConventionChecker$runChecks$1       : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinCompilation                : loaded 2 times (x 187B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.UnusedSourceSetsChecker : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.utils.Future                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishFlowAction$Parameters             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.statistics.KotlinBuildStatsConfiguration$Companion: loaded 2 times (x 67B)
Class com.google.common.util.concurrent.AbstractFuture$SetFuture                      : loaded 2 times (x 71B)
Class com.google.common.cache.AbstractCache$StatsCounter                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetSoftwareComponentImplKt      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinJsSubTargetContainerDsl        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.tooling.core.MutableExtrasImpl$Companion                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Companion$registerIfAbsent$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.statistics.fileloggers.MetricsContainer                    : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ProjectIsolationStartParameterAccessor: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.StoredProperty                                : loaded 2 times (x 66B)
Class com.google.common.collect.Iterators$MergingIterator                             : loaded 2 times (x 77B)
Class com.google.common.collect.RegularImmutableSet                                   : loaded 2 times (x 144B)
Class org.gradle.internal.installation.GradleInstallation                             : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompile$DefaultImpls                   : loaded 2 times (x 67B)
Class com.google.common.collect.ElementTypesAreNonnullByDefault                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.compilationImpl.factory.KotlinCompilationImplFactory$create$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.BuildFinishFlowAction                        : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanMetrics$Companion                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.CompatibilityConventionRegistrar    : loaded 2 times (x 66B)
Class com.google.common.collect.Iterables                                             : loaded 2 times (x 67B)
Class com.google.common.base.Splitter$Strategy                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinJvmCompilation                     : loaded 2 times (x 196B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation$Companion                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$WATCHOS_ARM64                     : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.konan.target.KonanTarget$MACOS_ARM64                       : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.diagnostics.checkers.OverriddenKotlinNativeHomeChecker: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinJvmJarArtifactKt$KotlinJvmJarArtifact$1: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.artifacts.KotlinLegacyCompatibilityMetadataArtifactKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.SetupEmbedAndSignAppleFrameworkTaskSideEffectKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinProjectSetupActionKt$KotlinProjectSetupCoroutine$1: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.dsl.ToolchainSupport$Companion                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSet                              : loaded 2 times (x 66B)

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22000 (10.0.22000.2538)
OS uptime: 0 days 3:18 hours

CPU: total 16 (initial active 16) (16 cores per cpu, 2 threads per core) family 23 model 96 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, rdpid, f16c
Processor Information for the first 16 processors :
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700

Memory: 4k page, system-wide physical 15697M (403M free)
TotalPageFile size 19793M (AvailPageFile size 29M)
current process WorkingSet (physical memory assigned to process): 1414M, peak: 1430M
current process commit charge ("private bytes"): 1469M, peak: 1645M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+-13368085-b895.109) for windows-amd64 JRE (21.0.6+-13368085-b895.109), built on 2025-04-16T17:01:31Z by "builder" with MS VC++ 16.10 / 16.11 (VS2019)

END.
